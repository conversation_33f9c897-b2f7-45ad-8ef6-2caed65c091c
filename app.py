# app.py
from flask import Flask, request, jsonify, send_from_directory
from flask_cors import CORS
import os
import json
from datetime import datetime
import openai
import google.generativeai as genai
from typing import Dict, List, Optional
import requests

app = Flask(__name__)
CORS(app)

# API配置
OPENAI_API_KEY = os.getenv('OPENAI_API_KEY', '')
DEEPSEEK_API_KEY = os.getenv('DEEPSEEK_API_KEY', '')
GEMINI_API_KEY = os.getenv('GEMINI_API_KEY', '')

# 初始化API客户端
if OPENAI_API_KEY:
    openai.api_key = OPENAI_API_KEY

if GEMINI_API_KEY:
    genai.configure(api_key=GEMINI_API_KEY)

class AIAssistant:
    """AI助手类，支持多个大模型"""

    def __init__(self):
        self.models = {
            'openai': self.call_openai,
            'deepseek': self.call_deepseek,
            'gemini': self.call_gemini
        }
        self.current_model = 'deepseek'  # 默认使用DeepSeek

    def call_openai(self, prompt: str, system_prompt: str = "") -> str:
        """调用OpenAI API"""
        if not OPENAI_API_KEY:
            return "OpenAI API密钥未配置"

        try:
            response = openai.ChatCompletion.create(
                model="gpt-4",
                messages=[
                    {"role": "system", "content": system_prompt},
                    {"role": "user", "content": prompt}
                ],
                temperature=0.7,
                max_tokens=2000
            )
            return response.choices[0].message['content']
        except Exception as e:
            return f"OpenAI API调用失败: {str(e)}"

    def call_deepseek(self, prompt: str, system_prompt: str = "") -> str:
        """调用DeepSeek API"""
        if not DEEPSEEK_API_KEY:
            return "DeepSeek API密钥未配置"

        try:
            headers = {
                'Authorization': f'Bearer {DEEPSEEK_API_KEY}',
                'Content-Type': 'application/json'
            }
            data = {
                'model': 'deepseek-chat',
                'messages': [
                    {"role": "system", "content": system_prompt},
                    {"role": "user", "content": prompt}
                ],
                'temperature': 0.7
            }
            response = requests.post(
                'https://api.deepseek.com/v1/chat/completions',
                headers=headers,
                json=data
            )
            return response.json()['choices'][0]['message']['content']
        except Exception as e:
            return f"DeepSeek API调用失败: {str(e)}"

    def call_gemini(self, prompt: str, system_prompt: str = "") -> str:
        """调用Gemini API"""
        if not GEMINI_API_KEY:
            return "Gemini API密钥未配置"

        try:
            model = genai.GenerativeModel('gemini-pro')
            full_prompt = f"{system_prompt}\n\n{prompt}" if system_prompt else prompt
            response = model.generate_content(full_prompt)
            return response.text
        except Exception as e:
            return f"Gemini API调用失败: {str(e)}"

    def generate_response(self, prompt: str, system_prompt: str = "", model: Optional[str] = None) -> str:
        """生成AI响应"""
        model_to_use = model or self.current_model
        if model_to_use in self.models:
            return self.models[model_to_use](prompt, system_prompt)
        return "指定的模型不可用"

# 初始化AI助手
ai_assistant = AIAssistant()

# 学习阶段模板
LEARNING_PHASES_PROMPTS = {
    0: "你是一位经验丰富的学习导师，正在帮助用户进入{domain}领域的全局认知阶段。请提供书籍推荐、学习路径规划等建议。",
    1: "你是一位信息管理专家，帮助用户在{domain}领域建立有效的信息获取渠道。请推荐资源订阅、信息筛选方法等。",
    2: "你是一位实践导师，指导用户在{domain}领域开始第一个项目。请提供项目选择、执行步骤等具体建议。",
    3: "你是一位人脉拓展顾问，帮助用户在{domain}领域建立专业网络。请提供社交策略、交流技巧等建议。",
    4: "你是一位创新教练，帮助用户将原有专长与{domain}领域结合。请提供跨界思维、创新方法等指导。",
    5: "你是一位系统化专家，帮助用户在{domain}领域建立完整的知识体系。请提供知识管理、持续学习等建议。"
}

@app.route('/api/generate-plan', methods=['POST'])
def generate_plan():
    """生成个性化学习计划"""
    data = request.json
    domain = data.get('domain', '')
    background = data.get('background', '')

    system_prompt = """你是一位专业的学习规划师。请根据用户的目标领域和背景，
    生成一个友好、鼓励性的欢迎消息，并简要说明接下来的学习路径。
    保持积极、专业但不失亲切的语气。"""

    prompt = f"""
    用户想要成为{domain}领域的专家。
    用户背景：{background if background else '无特殊背景'}

    请生成一个200字左右的欢迎消息，包括：
    1. 对用户选择的肯定
    2. 该领域的学习价值
    3. 简要的学习路径预览
    4. 鼓励和支持的话语
    """

    welcome_message = ai_assistant.generate_response(prompt, system_prompt)

    return jsonify({
        'status': 'success',
        'welcome_message': welcome_message,
        'domain': domain
    })

@app.route('/api/chat', methods=['POST'])
def chat():
    """处理聊天消息"""
    data = request.json
    message = data.get('message', '')
    domain = data.get('domain', '')
    phase = data.get('phase', '')
    context = data.get('context', {})
    model = data.get('model', 'deepseek')

    # 获取当前阶段的系统提示
    phase_index = context.get('currentPhase', 0)
    system_prompt = LEARNING_PHASES_PROMPTS.get(phase_index, "").format(domain=domain)

    # 构建完整的提示
    prompt = f"""
    当前学习领域：{domain}
    当前学习阶段：{phase}
    学习进度：{context.get('progress', 0):.1f}%

    用户问题：{message}

    请以专业、友好、具体的方式回答。如果可能，提供具体的行动建议。
    请使用Markdown格式来组织回答，包括标题、列表、粗体等。
    """

    response = ai_assistant.generate_response(prompt, system_prompt, model)

    return jsonify({
        'status': 'success',
        'response': response
    })

@app.route('/api/generate-mindmap', methods=['POST'])
def generate_mindmap():
    """生成知识图谱"""
    data = request.json
    domain = data.get('domain', '')
    phase = data.get('phase', 0)
    model = data.get('model', 'deepseek')

    system_prompt = "你是一位知识结构专家，擅长将复杂领域的知识体系化、结构化。"

    prompt = f"""
    为{domain}领域生成一个知识结构图谱。

    请严格按照以下JSON格式输出，不要添加任何其他文本：

    {{
        "domain": "{domain}",
        "core_concepts": [
            {{
                "name": "核心概念1",
                "subtopics": ["子主题1", "子主题2", "子主题3"],
                "skills": ["技能1", "技能2"]
            }},
            {{
                "name": "核心概念2",
                "subtopics": ["子主题1", "子主题2", "子主题3"],
                "skills": ["技能1", "技能2"]
            }}
        ],
        "learning_path": ["学习步骤1", "学习步骤2", "学习步骤3"]
    }}

    请为{domain}领域生成具体的内容，包含3-5个核心概念，每个概念下有3-4个子主题。
    """

    response = ai_assistant.generate_response(prompt, system_prompt, model)

    # 尝试解析JSON
    try:
        # 清理响应文本，只保留JSON部分
        cleaned_response = response.strip()
        if '```json' in cleaned_response:
            cleaned_response = cleaned_response.split('```json')[1].split('```')[0]
        elif '```' in cleaned_response:
            cleaned_response = cleaned_response.split('```')[1].split('```')[0]

        mindmap_data = json.loads(cleaned_response)

        # 验证数据结构
        if 'core_concepts' not in mindmap_data:
            raise ValueError("缺少core_concepts字段")

    except Exception as e:
        print(f"JSON解析失败: {e}")
        print(f"原始响应: {response}")
        # 如果解析失败，返回原始文本
        mindmap_data = {"raw_text": response}

    return jsonify({
        'status': 'success',
        'mindmap': mindmap_data
    })

@app.route('/api/get-resources', methods=['POST'])
def get_resources():
    """获取学习资源推荐"""
    data = request.json
    domain = data.get('domain', '')
    phase = data.get('phase', 0)
    model = data.get('model', 'deepseek')

    # 确保phase是整数
    try:
        phase = int(phase)
    except (ValueError, TypeError):
        phase = 0

    system_prompt = "你是一位学习资源专家，了解各个领域的优质学习材料。"

    prompt = f"""
    为正在学习{domain}的用户推荐学习资源。
    当前处于第{phase + 1}个学习阶段。

    请以JSON格式返回推荐资源，格式如下：
    {{
        "resources": [
            {{
                "title": "资源标题",
                "type": "书籍/课程/文章/视频/网站/工具",
                "description": "详细描述为什么推荐这个资源",
                "url": "真实的资源链接URL"
            }}
        ]
    }}

    请推荐5-8个高质量资源，包括：
    1. 必读书籍（2-3本）- 提供豆瓣读书或Amazon链接
    2. 在线课程（2-3个）- 提供Coursera、edX、Udemy等平台链接
    3. 专业网站/博客（2-3个）- 提供官方网站或知名博客链接
    4. 实践平台或工具 - 提供GitHub、官网等链接

    确保所有URL都是真实可访问的链接。
    """

    response = ai_assistant.generate_response(prompt, system_prompt, model)

    # 尝试解析JSON响应
    try:
        # 清理响应文本，只保留JSON部分
        cleaned_response = response.strip()
        if '```json' in cleaned_response:
            cleaned_response = cleaned_response.split('```json')[1].split('```')[0]
        elif '```' in cleaned_response:
            cleaned_response = cleaned_response.split('```')[1].split('```')[0]

        resource_data = json.loads(cleaned_response)

        # 验证数据结构
        if 'resources' in resource_data and isinstance(resource_data['resources'], list):
            resources = resource_data['resources']

            # 确保每个资源都有必要的字段
            for resource in resources:
                if 'url' not in resource:
                    # 如果没有URL，生成一个默认URL
                    resource['url'] = f"https://example.com/search?q={resource.get('title', '').replace(' ', '+')}"
        else:
            raise ValueError("缺少resources字段")

    except Exception as e:
        print(f"JSON解析失败: {e}")
        print(f"原始响应: {response}")

        # 如果解析失败，返回高质量的默认资源列表
        resources = [
            {
                'title': f'{domain}官方文档',
                'type': '文档',
                'description': '官方权威文档，包含完整的学习指南和参考资料',
                'url': f'https://www.google.com/search?q={domain.replace(" ", "+")}+official+documentation'
            },
            {
                'title': f'{domain}在线课程 - Coursera',
                'type': '课程',
                'description': '世界顶尖大学和公司提供的专业课程',
                'url': f'https://www.coursera.org/search?query={domain.replace(" ", "%20")}'
            },
            {
                'title': f'{domain}在线课程 - edX',
                'type': '课程',
                'description': '哈佛、MIT等名校的免费在线课程',
                'url': f'https://www.edx.org/search?q={domain.replace(" ", "%20")}'
            },
            {
                'title': f'{domain}实践项目 - GitHub',
                'type': '项目',
                'description': '开源项目和代码示例，动手实践学习',
                'url': f'https://github.com/search?q={domain.replace(" ", "+")}+tutorial'
            },
            {
                'title': f'{domain}问答社区 - Stack Overflow',
                'type': '社区',
                'description': '程序员问答社区，解决实际问题',
                'url': f'https://stackoverflow.com/questions/tagged/{domain.lower().replace(" ", "-")}'
            },
            {
                'title': f'{domain}技术博客 - Medium',
                'type': '博客',
                'description': '技术专家分享的最新见解和最佳实践',
                'url': f'https://medium.com/search?q={domain.replace(" ", "%20")}'
            },
            {
                'title': f'{domain}视频教程 - YouTube',
                'type': '视频',
                'description': '丰富的视频教程和实战演示',
                'url': f'https://www.youtube.com/results?search_query={domain.replace(" ", "+")}+tutorial'
            },
            {
                'title': f'{domain}书籍推荐 - 豆瓣读书',
                'type': '书籍',
                'description': '经典书籍推荐和读者评价',
                'url': f'https://book.douban.com/subject_search?search_text={domain.replace(" ", "+")}'
            }
        ]

    return jsonify({
        'status': 'success',
        'resources': resources[:8]  # 限制最多8个
    })

@app.route('/api/get-projects', methods=['POST'])
def get_projects():
    """获取实践项目推荐"""
    data = request.json
    domain = data.get('domain', '')
    phase = data.get('phase', 0)
    background = data.get('background', '')
    model = data.get('model', 'deepseek')

    # 确保phase是整数
    try:
        phase = int(phase)
    except (ValueError, TypeError):
        phase = 0

    system_prompt = "你是一位项目实践专家，擅长为不同水平的学习者设计合适的实践项目。"

    prompt = f"""
    为学习{domain}的用户推荐实践项目。
    用户背景：{background if background else '初学者'}
    当前处于第{phase + 1}个学习阶段。

    请推荐3-5个项目，从简单到复杂排序，每个项目包括：
    1. 项目名称
    2. 难度等级（初级/中级/高级）
    3. 项目描述（50字以内）
    4. 预计完成时间
    5. 能够学到的关键技能
    请使用Markdown格式组织回答。
    """

    response = ai_assistant.generate_response(prompt, system_prompt, model)

    # 简单解析项目信息
    projects = []
    project_texts = response.split('\n\n')

    for i, text in enumerate(project_texts[:5]):
        if text.strip():
            projects.append({
                'name': f'项目{i+1}',
                'difficulty': ['初级', '中级', '中级', '高级', '高级'][i] if i < 5 else '中级',
                'description': text.strip()[:100]
            })

    return jsonify({
        'status': 'success',
        'projects': projects
    })

@app.route('/api/weekly-review', methods=['POST'])
def weekly_review():
    """生成周度复盘"""
    data = request.json
    domain = data.get('domain', '')
    week = data.get('week', 1)
    progress = data.get('progress', 0)
    phase = data.get('phase', 0)
    model = data.get('model', 'deepseek')

    # 确保数值类型正确
    try:
        week = int(week)
        progress = float(progress)
        phase = int(phase)
    except (ValueError, TypeError):
        week = 1
        progress = 0.0
        phase = 0

    system_prompt = """你是一位学习教练，擅长帮助学员进行学习复盘和改进。
    请用鼓励、建设性的语气，既肯定成就也指出改进方向。"""

    prompt = f"""
    为学习{domain}的用户生成第{week}周的学习复盘。
    当前总体进度：{progress:.1f}%
    当前阶段：第{phase + 1}阶段

    请包含以下内容：
    1. 本周学习成就总结
    2. 遇到的挑战和解决建议
    3. 下周重点任务（3-5个）
    4. 学习方法优化建议
    5. 激励和鼓励的话

    保持积极正面，同时给出具体可行的建议。
    请使用Markdown格式组织回答。
    """

    review = ai_assistant.generate_response(prompt, system_prompt, model)

    return jsonify({
        'status': 'success',
        'review': review
    })

@app.route('/')
def index():
    """提供主页"""
    return send_from_directory('.', 'index.html')

@app.route('/<path:filename>')
def static_files(filename):
    """提供静态文件"""
    return send_from_directory('.', filename)

@app.route('/health', methods=['GET'])
def health_check():
    """健康检查接口"""
    return jsonify({
        'status': 'healthy',
        'timestamp': datetime.now().isoformat()
    })

if __name__ == '__main__':
    # 确保已设置至少一个API密钥
    if not any([OPENAI_API_KEY, DEEPSEEK_API_KEY, GEMINI_API_KEY]):
        print("警告：没有配置任何AI API密钥。请设置环境变量：")
        print("- OPENAI_API_KEY")
        print("- DEEPSEEK_API_KEY")
        print("- GEMINI_API_KEY")

    app.run(debug=True, host='0.0.0.0', port=8000)