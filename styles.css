/* styles.css */

/* 全局样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

:root {
    --primary-color: #4CAF50;
    --secondary-color: #2196F3;
    --accent-color: #FF9800;
    --background-color: #f5f7fa;
    --card-background: #ffffff;
    --text-primary: #333333;
    --text-secondary: #666666;
    --border-color: #e0e0e0;
    --shadow-sm: 0 2px 4px rgba(0,0,0,0.1);
    --shadow-md: 0 4px 8px rgba(0,0,0,0.1);
    --shadow-lg: 0 8px 16px rgba(0,0,0,0.1);
    --transition: all 0.3s ease;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, sans-serif;
    background-color: var(--background-color);
    color: var(--text-primary);
    line-height: 1.6;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
}

/* 头部样式 */
.header {
    text-align: center;
    padding: 40px 0;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    color: white;
    border-radius: 16px;
    margin-bottom: 30px;
    animation: fadeInDown 0.8s ease-out;
}

.logo {
    font-size: 3em;
    font-weight: 700;
    margin-bottom: 10px;
    letter-spacing: 2px;
}

.tagline {
    font-size: 1.2em;
    opacity: 0.9;
}

/* 主要内容区域 */
.main-content {
    animation: fadeIn 1s ease-out;
}

.section {
    display: none;
}

.section.active {
    display: block;
}

/* 设置卡片 */
.setup-card {
    background: var(--card-background);
    border-radius: 16px;
    padding: 40px;
    box-shadow: var(--shadow-lg);
    max-width: 600px;
    margin: 0 auto;
    animation: slideUp 0.6s ease-out;
}

.setup-card h2 {
    color: var(--primary-color);
    margin-bottom: 30px;
    text-align: center;
}

.input-group {
    margin-bottom: 25px;
}

.input-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 500;
    color: var(--text-secondary);
}

.input-group input,
.input-group textarea {
    width: 100%;
    padding: 12px 16px;
    border: 2px solid var(--border-color);
    border-radius: 8px;
    font-size: 16px;
    transition: var(--transition);
}

.input-group input:focus,
.input-group textarea:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(76, 175, 80, 0.1);
}

.input-group textarea {
    min-height: 100px;
    resize: vertical;
}

/* 按钮样式 */
.primary-btn {
    background: linear-gradient(135deg, var(--primary-color), #45a049);
    color: white;
    border: none;
    padding: 14px 32px;
    border-radius: 8px;
    font-size: 18px;
    font-weight: 600;
    cursor: pointer;
    transition: var(--transition);
    width: 100%;
    box-shadow: var(--shadow-md);
}

.primary-btn:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.primary-btn:active {
    transform: translateY(0);
}

/* 进度仪表板 */
.progress-overview {
    background: var(--card-background);
    border-radius: 16px;
    padding: 25px;
    margin-bottom: 20px;
    box-shadow: var(--shadow-md);
}

.progress-content {
    display: flex;
    align-items: center;
    gap: 30px;
    flex-wrap: wrap;
}

.progress-ring {
    position: relative;
    width: 150px;
    height: 150px;
    flex-shrink: 0;
}

.progress-text {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    text-align: center;
}

#progress-percent {
    display: block;
    font-size: 2.2em;
    font-weight: 700;
    color: var(--primary-color);
}

.progress-label {
    display: block;
    color: var(--text-secondary);
    font-size: 0.9em;
}

#progress-circle {
    transition: stroke-dashoffset 1s ease-out;
}

/* 统计信息 */
.stats-info {
    display: flex;
    gap: 30px;
    flex: 1;
    min-width: 300px;
}

.stat-item {
    background: var(--background-color);
    padding: 15px 20px;
    border-radius: 12px;
    text-align: center;
    flex: 1;
}

.stat-item h3 {
    color: var(--primary-color);
    margin-bottom: 5px;
    font-size: 1.1em;
}

.stat-item p {
    font-size: 1.1em;
    color: var(--text-secondary);
    margin: 0;
}

/* 模型选择器 */
.model-selector {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-left: auto;
}

.model-selector label {
    font-weight: 500;
    color: var(--text-secondary);
    white-space: nowrap;
}

.model-select {
    padding: 8px 12px;
    border: 2px solid var(--border-color);
    border-radius: 8px;
    background: white;
    font-size: 14px;
    cursor: pointer;
    transition: var(--transition);
}

.model-select:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(76, 175, 80, 0.1);
}

/* 主要仪表板布局 */
.main-dashboard {
    display: grid;
    grid-template-columns: 350px 1fr;
    gap: 20px;
    min-height: 600px;
    align-items: start;
}

/* 阶段侧边栏 */
.phases-sidebar {
    background: var(--card-background);
    border-radius: 16px;
    padding: 20px;
    box-shadow: var(--shadow-md);
    height: fit-content;
    min-height: 500px;
}

/* 阶段容器 */
.phases-container h3 {
    margin: 0 0 15px 0;
    color: var(--primary-color);
    font-size: 1.3em;
}

.phases-list {
    display: grid;
    gap: 12px;
}

.phase-card {
    background: var(--background-color);
    border-radius: 10px;
    padding: 15px;
    transition: var(--transition);
    cursor: pointer;
    border: 2px solid transparent;
    position: relative;
}

.phase-card:hover {
    transform: translateX(5px);
    border-color: var(--primary-color);
    box-shadow: var(--shadow-sm);
}

.phase-card.active {
    background: linear-gradient(135deg, rgba(76, 175, 80, 0.1), rgba(33, 150, 243, 0.1));
    border-color: var(--primary-color);
}

.phase-card.completed {
    opacity: 0.7;
    background: rgba(76, 175, 80, 0.05);
}

.phase-card.completed::after {
    content: '✓';
    position: absolute;
    top: 10px;
    right: 10px;
    color: var(--primary-color);
    font-weight: bold;
    font-size: 1.2em;
}

.phase-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
}

.phase-name {
    font-weight: 600;
    color: var(--text-primary);
    font-size: 0.95em;
}

.phase-status {
    font-size: 0.8em;
    color: var(--text-secondary);
}

.phase-description {
    color: var(--text-secondary);
    font-size: 0.85em;
    line-height: 1.4;
}

/* 阶段详情 */
.phase-details {
    animation: slideIn 0.3s ease-out;
}

.phase-details-header {
    margin-bottom: 15px;
}

.back-btn {
    background: none;
    border: none;
    color: var(--primary-color);
    cursor: pointer;
    font-size: 14px;
    padding: 5px 0;
    transition: var(--transition);
}

.back-btn:hover {
    color: var(--secondary-color);
}

.phase-details-content {
    background: var(--background-color);
    border-radius: 10px;
    padding: 15px;
}

.phase-task-list {
    list-style: none;
    padding: 0;
    margin: 10px 0;
}

.phase-task-list li {
    padding: 8px 0;
    border-bottom: 1px solid var(--border-color);
    font-size: 0.9em;
    color: var(--text-secondary);
}

.phase-task-list li:last-child {
    border-bottom: none;
}

.phase-task-list li::before {
    content: '•';
    color: var(--primary-color);
    margin-right: 8px;
}

/* 阶段详情样式 */
.phase-detail-header {
    margin-bottom: 15px;
    padding-bottom: 10px;
    border-bottom: 1px solid var(--border-color);
}

.phase-detail-header h4 {
    margin: 0 0 8px 0;
    color: var(--primary-color);
    font-size: 1.2em;
}

.phase-progress-info {
    display: flex;
    gap: 15px;
    align-items: center;
}

.phase-duration {
    font-size: 0.9em;
    color: var(--text-secondary);
}

.phase-status-badge {
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 0.8em;
    font-weight: 500;
}

.phase-status-badge.completed {
    background: rgba(76, 175, 80, 0.2);
    color: var(--primary-color);
}

.phase-status-badge.active {
    background: rgba(33, 150, 243, 0.2);
    color: var(--secondary-color);
}

.phase-status-badge.pending {
    background: rgba(158, 158, 158, 0.2);
    color: var(--text-secondary);
}

.phase-description-detail {
    margin-bottom: 15px;
}

.phase-description-detail p {
    color: var(--text-secondary);
    line-height: 1.5;
    margin: 0;
}

.phase-tasks-section h5 {
    margin: 0 0 10px 0;
    color: var(--text-primary);
    font-size: 1em;
}

.phase-actions {
    margin-top: 15px;
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
}

.phase-action-btn {
    background: var(--primary-color);
    color: white;
    border: none;
    padding: 8px 16px;
    border-radius: 6px;
    font-size: 0.9em;
    cursor: pointer;
    transition: var(--transition);
}

.phase-action-btn:hover {
    background: #45a049;
    transform: translateY(-1px);
}

/* AI助手主区域 */
.ai-assistant-main {
    background: var(--card-background);
    border-radius: 16px;
    box-shadow: var(--shadow-md);
    height: fit-content;
    min-height: 500px;
    display: flex;
    flex-direction: column;
}

.ai-assistant {
    padding: 20px;
    height: 100%;
    display: flex;
    flex-direction: column;
}

.ai-assistant-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
    padding-bottom: 15px;
    border-bottom: 1px solid var(--border-color);
}

.ai-assistant-header h3 {
    margin: 0;
    color: var(--primary-color);
    font-size: 1.3em;
}

/* 快速操作按钮 */
.quick-actions {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 12px;
    margin-top: 15px;
    padding-top: 15px;
    border-top: 1px solid var(--border-color);
}

.action-btn {
    background: var(--background-color);
    border: 2px solid var(--border-color);
    padding: 12px 16px;
    border-radius: 10px;
    cursor: pointer;
    transition: var(--transition);
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 14px;
    color: var(--text-primary);
    box-shadow: var(--shadow-sm);
}

.action-btn:hover {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

.action-icon {
    font-size: 18px;
    flex-shrink: 0;
}

.action-text {
    font-weight: 500;
    white-space: nowrap;
}

.chat-container {
    flex: 1;
    display: flex;
    flex-direction: column;
    min-height: 500px;
}

.chat-messages {
    flex: 1;
    overflow-y: auto;
    border: 1px solid var(--border-color);
    border-radius: 12px;
    padding: 15px;
    margin-bottom: 15px;
    background: var(--background-color);
    min-height: 400px;
    max-height: 500px;
}

.message {
    margin-bottom: 16px;
    animation: slideIn 0.3s ease-out;
}

.message.user {
    text-align: right;
}

.message-bubble {
    display: inline-block;
    max-width: 80%;
    padding: 12px 16px;
    border-radius: 16px;
    word-wrap: break-word;
    line-height: 1.4;
}

.message.user .message-bubble {
    background: var(--primary-color);
    color: white;
}

.message.ai .message-bubble {
    background: white;
    border: 1px solid var(--border-color);
    color: var(--text-primary);
}

/* 流式显示效果 */
.message.ai .message-bubble.streaming {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px rgba(76, 175, 80, 0.1);
}

.message.ai .message-bubble.streaming::after {
    content: '▊';
    animation: blink 1s infinite;
    color: var(--primary-color);
}

@keyframes blink {
    0%, 50% { opacity: 1; }
    51%, 100% { opacity: 0; }
}

/* 思考标签 */
.thinking-tag {
    background: rgba(76, 175, 80, 0.1);
    border: 1px solid var(--primary-color);
    border-radius: 8px;
    padding: 8px 12px;
    margin: 8px 0;
    font-size: 0.9em;
    color: var(--primary-color);
    cursor: pointer;
    transition: var(--transition);
}

.thinking-tag:hover {
    background: rgba(76, 175, 80, 0.2);
}

.thinking-tag.collapsed::after {
    content: ' ▼';
}

.thinking-tag.expanded::after {
    content: ' ▲';
}

.thinking-content {
    background: rgba(76, 175, 80, 0.05);
    border-radius: 6px;
    padding: 10px;
    margin-top: 5px;
    font-size: 0.85em;
    color: var(--text-secondary);
    border-left: 3px solid var(--primary-color);
}

.thinking-content.hidden {
    display: none;
}

.chat-input-area {
    display: flex;
    gap: 10px;
    margin-top: auto;
}

#chat-input {
    flex: 1;
    padding: 12px 16px;
    border: 2px solid var(--border-color);
    border-radius: 8px;
    font-size: 16px;
    transition: var(--transition);
    resize: none;
    min-height: 44px;
    max-height: 120px;
}

#chat-input:focus {
    outline: none;
    border-color: var(--primary-color);
}

.send-btn {
    background: var(--primary-color);
    color: white;
    border: none;
    padding: 12px 20px;
    border-radius: 8px;
    font-weight: 600;
    cursor: pointer;
    transition: var(--transition);
    white-space: nowrap;
}

.send-btn:hover {
    background: #45a049;
    transform: translateY(-1px);
}

.send-btn:disabled {
    background: var(--text-secondary);
    cursor: not-allowed;
    transform: none;
}

/* 知识图谱样式 */
.mindmap-visualization {
    width: 100%;
    height: 500px;
    border: 1px solid var(--border-color);
    border-radius: 12px;
    background: var(--background-color);
    position: relative;
    overflow: hidden;
    display: flex;
    align-items: center;
    justify-content: center;
}

.mindmap-svg {
    width: 100%;
    height: 100%;
    background: transparent;
}

/* SVG元素层级控制 */
.mindmap-svg #connections {
    z-index: 1;
}

.mindmap-svg #nodes {
    z-index: 2;
}

.mindmap-node {
    background: var(--card-background);
    border: 2px solid var(--primary-color);
    border-radius: 8px;
    padding: 6px 12px;
    font-size: 0.85em;
    cursor: pointer;
    transition: var(--transition);
    box-shadow: var(--shadow-sm);
    text-align: center;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 120px;
    position: relative;
    z-index: 10;
    /* 确保节点有实体背景，遮盖连接线 */
    background-clip: padding-box;
}

.mindmap-node:hover {
    transform: scale(1.1);
    box-shadow: var(--shadow-md);
    z-index: 10;
}

.mindmap-node.root {
    background: var(--primary-color);
    color: white;
    font-weight: 600;
    font-size: 1em;
    padding: 10px 16px;
    max-width: 150px;
    border-radius: 12px;
    z-index: 15;
    box-shadow: var(--shadow-md);
}

.mindmap-node.level-1 {
    background: var(--card-background);
    border-color: var(--primary-color);
    font-weight: 500;
    max-width: 100px;
    z-index: 12;
    /* 加强背景不透明度 */
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(2px);
}

.mindmap-node.level-2 {
    background: var(--card-background);
    border-color: var(--secondary-color);
    font-size: 0.8em;
    padding: 6px 10px;
    max-width: 90px;
    z-index: 11;
    /* 加强背景不透明度 */
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(2px);
    /* 增加边距，避免重叠 */
    margin: 2px;
}

.mindmap-connection {
    position: absolute;
    height: 2px;
    background: var(--primary-color);
    opacity: 0.6;
}

/* SVG路径动画 */
@keyframes drawPath {
    to {
        stroke-dashoffset: 0;
    }
}

/* Toast通知动画 */
@keyframes slideInRight {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

@keyframes slideOutRight {
    from {
        transform: translateX(0);
        opacity: 1;
    }
    to {
        transform: translateX(100%);
        opacity: 0;
    }
}

@keyframes slideInLeft {
    from {
        transform: translateX(-100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

@keyframes slideOutLeft {
    from {
        transform: translateX(0);
        opacity: 1;
    }
    to {
        transform: translateX(-100%);
        opacity: 0;
    }
}

@keyframes phaseAdvanceIn {
    from {
        transform: translate(-50%, -50%) scale(0.8);
        opacity: 0;
    }
    to {
        transform: translate(-50%, -50%) scale(1);
        opacity: 1;
    }
}

@keyframes phaseAdvanceOut {
    from {
        transform: translate(-50%, -50%) scale(1);
        opacity: 1;
    }
    to {
        transform: translate(-50%, -50%) scale(0.8);
        opacity: 0;
    }
}

/* 知识图谱内容区域 */
.mindmap-content {
    padding: 20px;
}

.mindmap-content h3 {
    text-align: center;
    margin-bottom: 20px;
    color: var(--primary-color);
    font-size: 1.3em;
}

.mindmap-text {
    background: white;
    padding: 20px;
    border-radius: 8px;
    border: 1px solid var(--border-color);
    line-height: 1.6;
}

/* 模态框 */
.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0,0,0,0.5);
    animation: fadeIn 0.3s ease-out;
}

.modal-content {
    background-color: white;
    margin: 5% auto;
    padding: 40px;
    border-radius: 16px;
    width: 90%;
    max-width: 800px;
    box-shadow: var(--shadow-lg);
    animation: slideUp 0.3s ease-out;
}

.close {
    color: var(--text-secondary);
    float: right;
    font-size: 28px;
    font-weight: bold;
    cursor: pointer;
    transition: var(--transition);
}

.close:hover {
    color: var(--text-primary);
}

.mindmap-container {
    margin-top: 20px;
    min-height: 400px;
    background: var(--background-color);
    border-radius: 12px;
    padding: 20px;
}

/* 动画 */
@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes fadeInDown {
    from {
        opacity: 0;
        transform: translateY(-30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideIn {
    from {
        opacity: 0;
        transform: translateX(20px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

/* 响应式设计 */
@media (max-width: 1024px) {
    .main-dashboard {
        grid-template-columns: 300px 1fr;
        gap: 15px;
    }

    .phases-sidebar {
        padding: 15px;
    }
}

@media (max-width: 768px) {
    .container {
        padding: 10px;
    }

    .setup-card {
        padding: 30px 20px;
    }

    .logo {
        font-size: 2.2em;
    }

    .progress-content {
        flex-direction: column;
        align-items: center;
        text-align: center;
    }

    .stats-info {
        flex-direction: column;
        gap: 15px;
        width: 100%;
    }

    .model-selector {
        margin-left: 0;
        margin-top: 15px;
    }

    .main-dashboard {
        grid-template-columns: 1fr;
        gap: 15px;
    }

    .phases-sidebar {
        order: 2;
    }

    .ai-assistant-main {
        order: 1;
        min-height: 400px;
    }

    .chat-messages {
        min-height: 300px;
        max-height: 400px;
    }

    .message-bubble {
        max-width: 90%;
    }

    .quick-actions {
        grid-template-columns: 1fr;
        gap: 10px;
    }

    .action-btn {
        padding: 10px 12px;
        font-size: 13px;
    }

    .action-icon {
        font-size: 16px;
    }
}

@media (max-width: 480px) {
    .progress-ring {
        width: 120px;
        height: 120px;
    }

    .progress-ring svg {
        width: 120px;
        height: 120px;
    }

    .progress-ring circle {
        r: 50;
        cx: 60;
        cy: 60;
        stroke-dasharray: 314;
    }

    #progress-percent {
        font-size: 1.8em;
    }

    .ai-assistant {
        padding: 15px;
    }

    .chat-messages {
        padding: 10px;
        min-height: 250px;
    }

    .phases-sidebar {
        padding: 15px;
    }

    .phase-card {
        padding: 12px;
    }
}