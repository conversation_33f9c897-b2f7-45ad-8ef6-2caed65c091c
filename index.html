<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>半年专家 - 快速成长学习系统</title>
    <link rel="stylesheet" href="styles.css">
</head>
<body>
    <div class="container">
        <!-- 头部导航 -->
        <header class="header">
            <h1 class="logo">半年专家</h1>
            <p class="tagline">系统化学习，快速成为领域专家</p>
        </header>

        <!-- 主要内容区域 -->
        <main class="main-content">
            <!-- 学习领域设置 -->
            <section id="setup-section" class="section active">
                <div class="setup-card">
                    <h2>开始您的专家之旅</h2>
                    <div class="input-group">
                        <label for="domain-input">您想成为哪个领域的专家？</label>
                        <input type="text" id="domain-input" placeholder="例如：机器学习、产品设计、投资理财...">
                    </div>
                    <div class="input-group">
                        <label for="background-input">您的背景和经验（可选）</label>
                        <textarea id="background-input" placeholder="简单描述您的背景，帮助AI更好地定制学习计划..."></textarea>
                    </div>
                    <button id="start-btn" class="primary-btn">生成学习计划</button>
                </div>
            </section>

            <!-- 学习进度仪表板 -->
            <section id="dashboard-section" class="section">
                <!-- 顶部进度概览 -->
                <div class="progress-overview">
                    <h2>学习进度总览</h2>
                    <div class="progress-content">
                        <div class="progress-ring">
                            <svg width="150" height="150">
                                <circle cx="75" cy="75" r="65" fill="none" stroke="#e0e0e0" stroke-width="8"/>
                                <circle id="progress-circle" cx="75" cy="75" r="65" fill="none" stroke="#4CAF50"
                                        stroke-width="8" stroke-dasharray="408.4" stroke-dashoffset="408.4"
                                        transform="rotate(-90 75 75)"/>
                            </svg>
                            <div class="progress-text">
                                <span id="progress-percent">0%</span>
                                <span class="progress-label">完成度</span>
                            </div>
                        </div>
                        <div class="stats-info">
                            <div class="stat-item">
                                <h3 id="current-phase">全局认知阶段</h3>
                                <p id="days-elapsed">第 0 天</p>
                            </div>
                            <div class="stat-item">
                                <h3>学习领域</h3>
                                <p id="learning-domain">-</p>
                            </div>
                        </div>
                        <!-- 模型选择器 -->
                        <div class="model-selector">
                            <label for="ai-model-select">AI模型:</label>
                            <select id="ai-model-select" class="model-select">
                                <option value="deepseek" selected>DeepSeek</option>
                                <option value="openai">OpenAI GPT-4</option>
                                <option value="gemini">Google Gemini</option>
                            </select>
                        </div>
                    </div>
                </div>

                <!-- 主要内容区域 -->
                <div class="main-dashboard">
                    <!-- 左侧：学习阶段 -->
                    <div class="phases-sidebar">
                        <div class="phases-container">
                            <h3>学习阶段</h3>
                            <div id="phases-list" class="phases-list">
                                <!-- 动态生成阶段卡片 -->
                            </div>
                            <!-- 阶段详情 -->
                            <div id="phase-details" class="phase-details" style="display: none;">
                                <div class="phase-details-header">
                                    <button id="back-to-phases" class="back-btn">← 返回阶段列表</button>
                                </div>
                                <div id="phase-details-content" class="phase-details-content">
                                    <!-- 动态生成阶段详情 -->
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 右侧：AI助手区域 -->
                    <div class="ai-assistant-main">
                        <div class="ai-assistant">
                            <div class="ai-assistant-header">
                                <h3>AI学习助手</h3>
                            </div>
                            <div class="chat-container">
                                <div id="chat-messages" class="chat-messages"></div>
                                <div class="chat-input-area">
                                    <input type="text" id="chat-input" placeholder="询问任何学习相关问题...">
                                    <button id="send-btn" class="send-btn">发送</button>
                                </div>
                                <!-- 快速操作按钮 -->
                                <div class="quick-actions">
                                    <button id="generate-mindmap-btn" class="action-btn">
                                        <span class="action-icon">🗺️</span>
                                        <span class="action-text">生成知识图谱</span>
                                    </button>
                                    <button id="get-resources-btn" class="action-btn">
                                        <span class="action-icon">📚</span>
                                        <span class="action-text">获取学习资源</span>
                                    </button>
                                    <button id="practice-project-btn" class="action-btn">
                                        <span class="action-icon">🛠️</span>
                                        <span class="action-text">推荐实践项目</span>
                                    </button>
                                    <button id="weekly-review-btn" class="action-btn">
                                        <span class="action-icon">📊</span>
                                        <span class="action-text">周度复盘</span>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            <!-- 知识图谱展示 -->
            <div id="mindmap-modal" class="modal">
                <div class="modal-content">
                    <span class="close">&times;</span>
                    <h2>知识结构图谱</h2>
                    <div id="mindmap-container" class="mindmap-container"></div>
                </div>
            </div>
        </main>
    </div>

    <script src="script.js"></script>
</body>
</html>