<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>半年专家 - 快速成长学习系统</title>
    <link rel="stylesheet" href="styles.css">
</head>
<body>
    <div class="container">
        <!-- 头部导航 -->
        <header class="header">
            <h1 class="logo">半年专家</h1>
            <p class="tagline">系统化学习，快速成为领域专家</p>
        </header>

        <!-- 主要内容区域 -->
        <main class="main-content">
            <!-- 学习领域设置 -->
            <section id="setup-section" class="section active">
                <div class="setup-card">
                    <h2>开始您的专家之旅</h2>
                    <div class="input-group">
                        <label for="domain-input">您想成为哪个领域的专家？</label>
                        <input type="text" id="domain-input" placeholder="例如：机器学习、产品设计、投资理财...">
                    </div>
                    <div class="input-group">
                        <label for="background-input">您的背景和经验（可选）</label>
                        <textarea id="background-input" placeholder="简单描述您的背景，帮助AI更好地定制学习计划..."></textarea>
                    </div>
                    <button id="start-btn" class="primary-btn">生成学习计划</button>
                </div>
            </section>

            <!-- 学习进度仪表板 -->
            <section id="dashboard-section" class="section">
                <div class="progress-overview">
                    <h2>学习进度总览</h2>
                    <div class="progress-ring">
                        <svg width="200" height="200">
                            <circle cx="100" cy="100" r="90" fill="none" stroke="#e0e0e0" stroke-width="10"/>
                            <circle id="progress-circle" cx="100" cy="100" r="90" fill="none" stroke="#4CAF50" 
                                    stroke-width="10" stroke-dasharray="565.48" stroke-dashoffset="565.48"
                                    transform="rotate(-90 100 100)"/>
                        </svg>
                        <div class="progress-text">
                            <span id="progress-percent">0%</span>
                            <span class="progress-label">完成度</span>
                        </div>
                    </div>
                    <div class="stats-grid">
                        <div class="stat-card">
                            <h3 id="current-phase">全局认知阶段</h3>
                            <p id="days-elapsed">第 0 天</p>
                        </div>
                        <div class="stat-card">
                            <h3>学习领域</h3>
                            <p id="learning-domain">-</p>
                        </div>
                    </div>
                </div>

                <!-- 阶段任务卡片 -->
                <div class="phases-container">
                    <h3>学习阶段</h3>
                    <div id="phases-list" class="phases-list">
                        <!-- 动态生成阶段卡片 -->
                    </div>
                </div>

                <!-- AI助手区域 -->
                <div class="ai-assistant">
                    <h3>AI学习助手</h3>
                    <div class="chat-container">
                        <div id="chat-messages" class="chat-messages"></div>
                        <div class="chat-input-area">
                            <input type="text" id="chat-input" placeholder="询问任何学习相关问题...">
                            <button id="send-btn" class="send-btn">发送</button>
                        </div>
                    </div>
                </div>

                <!-- 快速操作按钮 -->
                <div class="quick-actions">
                    <button id="generate-mindmap-btn" class="action-btn">生成知识图谱</button>
                    <button id="get-resources-btn" class="action-btn">获取学习资源</button>
                    <button id="practice-project-btn" class="action-btn">推荐实践项目</button>
                    <button id="weekly-review-btn" class="action-btn">周度复盘</button>
                </div>
            </section>

            <!-- 知识图谱展示 -->
            <div id="mindmap-modal" class="modal">
                <div class="modal-content">
                    <span class="close">&times;</span>
                    <h2>知识结构图谱</h2>
                    <div id="mindmap-container" class="mindmap-container"></div>
                </div>
            </div>
        </main>
    </div>

    <script src="script.js"></script>
</body>
</html>