// script.js

// 全局变量
let userProfile = {
    domain: '',
    background: '',
    startDate: null,
    currentPhase: 0,
    progress: 0
};

// API配置
const API_BASE_URL = 'http://localhost:8000';

// 学习阶段定义
const learningPhases = [
    {
        name: '全局认知阶段',
        duration: 14,
        description: '完成领域经典书籍快速阅读，生成知识结构图谱',
        tasks: ['阅读3本核心书籍', '制作思维导图', '确定学习路径']
    },
    {
        name: '信息浸泡阶段',
        duration: 168,
        description: '持续关注行业动态，建立信息获取渠道',
        tasks: ['订阅10个专业资源', '每日浏览30分钟', '收集100个案例']
    },
    {
        name: '实践起步阶段',
        duration: 14,
        description: '选择简单项目开始实践，积累经验',
        tasks: ['完成1个小项目', '记录所有问题', '形成解决方案库']
    },
    {
        name: '交流提升阶段',
        duration: 28,
        description: '与专家交流，参与社群讨论',
        tasks: ['联系3位专家', '加入2个专业社群', '发表5篇见解']
    },
    {
        name: '跨界融合阶段',
        duration: 56,
        description: '结合原有专长，创造独特价值',
        tasks: ['找到3个结合点', '开发独特方法', '创建特色作品']
    },
    {
        name: '系统优化阶段',
        duration: 56,
        description: '补充短板，建立知识体系',
        tasks: ['识别薄弱环节', '建立知识库', '形成学习循环']
    }
];

// DOM元素
const setupSection = document.getElementById('setup-section');
const dashboardSection = document.getElementById('dashboard-section');
const domainInput = document.getElementById('domain-input');
const backgroundInput = document.getElementById('background-input');
const startBtn = document.getElementById('start-btn');
const progressCircle = document.getElementById('progress-circle');
const progressPercent = document.getElementById('progress-percent');
const currentPhaseEl = document.getElementById('current-phase');
const daysElapsedEl = document.getElementById('days-elapsed');
const learningDomainEl = document.getElementById('learning-domain');
const phasesList = document.getElementById('phases-list');
const chatMessages = document.getElementById('chat-messages');
const chatInput = document.getElementById('chat-input');
const sendBtn = document.getElementById('send-btn');
const mindmapModal = document.getElementById('mindmap-modal');

// 事件监听器
startBtn.addEventListener('click', startLearning);
sendBtn.addEventListener('click', sendMessage);
chatInput.addEventListener('keypress', (e) => {
    if (e.key === 'Enter') sendMessage();
});

// 快速操作按钮
document.getElementById('generate-mindmap-btn').addEventListener('click', generateMindmap);
document.getElementById('get-resources-btn').addEventListener('click', getResources);
document.getElementById('practice-project-btn').addEventListener('click', getPracticeProjects);
document.getElementById('weekly-review-btn').addEventListener('click', weeklyReview);

// 模态框关闭
document.querySelector('.close').addEventListener('click', () => {
    mindmapModal.style.display = 'none';
});

// 开始学习
async function startLearning() {
    const domain = domainInput.value.trim();
    const background = backgroundInput.value.trim();
    
    if (!domain) {
        showNotification('请输入您想学习的领域', 'error');
        return;
    }
    
    userProfile.domain = domain;
    userProfile.background = background;
    userProfile.startDate = new Date();
    
    // 显示加载状态
    startBtn.textContent = '生成中...';
    startBtn.disabled = true;
    
    try {
        // 调用API生成学习计划
        const response = await fetch(`${API_BASE_URL}/api/generate-plan`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                domain: domain,
                background: background
            })
        });
        
        if (response.ok) {
            const data = await response.json();
            // 切换到仪表板
            setupSection.classList.remove('active');
            dashboardSection.classList.add('active');
            
            // 初始化仪表板
            initializeDashboard();
            
            // 显示AI欢迎消息
            addMessage('ai', data.welcome_message || `太棒了！让我们开始您的${domain}专家之旅。我会全程陪伴并指导您的学习。`);
        }
    } catch (error) {
        console.error('Error:', error);
        showNotification('生成学习计划时出错，请重试', 'error');
    } finally {
        startBtn.textContent = '生成学习计划';
        startBtn.disabled = false;
    }
}

// 初始化仪表板
function initializeDashboard() {
    learningDomainEl.textContent = userProfile.domain;
    updateProgress();
    renderPhases();
    
    // 定时更新进度
    setInterval(updateProgress, 60000); // 每分钟更新一次
}

// 更新进度
function updateProgress() {
    if (!userProfile.startDate) return;
    
    const now = new Date();
    const elapsed = Math.floor((now - userProfile.startDate) / (1000 * 60 * 60 * 24)); // 天数
    const totalDays = 180; // 6个月
    const progress = Math.min((elapsed / totalDays) * 100, 100);
    
    userProfile.progress = progress;
    daysElapsedEl.textContent = `第 ${elapsed} 天`;
    
    // 更新进度环
    const circumference = 2 * Math.PI * 90;
    const offset = circumference - (progress / 100) * circumference;
    progressCircle.style.strokeDashoffset = offset;
    progressPercent.textContent = `${Math.round(progress)}%`;
    
    // 更新当前阶段
    let totalPhaseDays = 0;
    for (let i = 0; i < learningPhases.length; i++) {
        totalPhaseDays += learningPhases[i].duration;
        if (elapsed <= totalPhaseDays) {
            userProfile.currentPhase = i;
            currentPhaseEl.textContent = learningPhases[i].name;
            break;
        }
    }
    
    // 更新阶段卡片状态
    updatePhaseCards();
}

// 渲染学习阶段
function renderPhases() {
    phasesList.innerHTML = '';
    
    learningPhases.forEach((phase, index) => {
        const phaseCard = document.createElement('div');
        phaseCard.className = 'phase-card';
        phaseCard.dataset.phase = index;
        
        phaseCard.innerHTML = `
            <div class="phase-header">
                <span class="phase-name">${phase.name}</span>
                <span class="phase-status">预计${phase.duration}天</span>
            </div>
            <div class="phase-description">${phase.description}</div>
        `;
        
        phaseCard.addEventListener('click', () => showPhaseTasks(phase));
        phasesList.appendChild(phaseCard);
    });
    
    updatePhaseCards();
}

// 更新阶段卡片状态
function updatePhaseCards() {
    const cards = document.querySelectorAll('.phase-card');
    cards.forEach((card, index) => {
        card.classList.remove('active', 'completed');
        if (index < userProfile.currentPhase) {
            card.classList.add('completed');
        } else if (index === userProfile.currentPhase) {
            card.classList.add('active');
        }
    });
}

// 显示阶段任务
function showPhaseTasks(phase) {
    const tasksHtml = phase.tasks.map(task => `<li>${task}</li>`).join('');
    addMessage('ai', `
        <strong>${phase.name}的关键任务：</strong>
        <ul>${tasksHtml}</ul>
        需要我为您提供具体指导吗？
    `);
}

// 发送消息
async function sendMessage() {
    const message = chatInput.value.trim();
    if (!message) return;
    
    // 添加用户消息
    addMessage('user', message);
    chatInput.value = '';
    
    // 显示加载状态
    const loadingId = addMessage('ai', '思考中...');
    
    try {
        const response = await fetch(`${API_BASE_URL}/api/chat`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                message: message,
                domain: userProfile.domain,
                phase: learningPhases[userProfile.currentPhase].name,
                context: userProfile
            })
        });
        
        if (response.ok) {
            const data = await response.json();
            // 更新AI回复
            updateMessage(loadingId, data.response);
        }
    } catch (error) {
        console.error('Error:', error);
        updateMessage(loadingId, '抱歉，发生了错误。请稍后重试。');
    }
}

// 添加消息到聊天框
function addMessage(type, content) {
    const messageDiv = document.createElement('div');
    messageDiv.className = `message ${type}`;
    const messageId = Date.now();
    messageDiv.id = `msg-${messageId}`;
    
    messageDiv.innerHTML = `
        <div class="message-bubble">${content}</div>
    `;
    
    chatMessages.appendChild(messageDiv);
    chatMessages.scrollTop = chatMessages.scrollHeight;
    
    return messageId;
}

// 更新消息
function updateMessage(messageId, newContent) {
    const messageEl = document.getElementById(`msg-${messageId}`);
    if (messageEl) {
        messageEl.querySelector('.message-bubble').innerHTML = newContent;
    }
}

// 生成知识图谱
async function generateMindmap() {
    mindmapModal.style.display = 'block';
    const mindmapContainer = document.getElementById('mindmap-container');
    mindmapContainer.innerHTML = '<p>正在生成知识图谱...</p>';
    
    try {
        const response = await fetch(`${API_BASE_URL}/api/generate-mindmap`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                domain: userProfile.domain,
                phase: userProfile.currentPhase
            })
        });
        
        if (response.ok) {
            const data = await response.json();
            displayMindmap(data.mindmap);
        }
    } catch (error) {
        console.error('Error:', error);
        mindmapContainer.innerHTML = '<p>生成知识图谱失败，请重试</p>';
    }
}

// 显示知识图谱
function displayMindmap(mindmapData) {
    const container = document.getElementById('mindmap-container');
    // 这里可以集成实际的思维导图库，如D3.js或其他
    // 简化版本：显示结构化文本
    container.innerHTML = `
        <div class="mindmap-content">
            <h3>${userProfile.domain}知识结构</h3>
            <pre>${JSON.stringify(mindmapData, null, 2)}</pre>
        </div>
    `;
}

// 获取学习资源
async function getResources() {
    addMessage('ai', '正在为您查找最新的学习资源...');
    
    try {
        const response = await fetch(`${API_BASE_URL}/api/get-resources`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                domain: userProfile.domain,
                phase: userProfile.currentPhase
            })
        });
        
        if (response.ok) {
            const data = await response.json();
            const resourcesHtml = data.resources.map(r => 
                `<li><strong>${r.title}</strong> - ${r.type}<br>${r.description}</li>`
            ).join('');
            
            addMessage('ai', `
                <strong>为您推荐的学习资源：</strong>
                <ul>${resourcesHtml}</ul>
            `);
        }
    } catch (error) {
        console.error('Error:', error);
        addMessage('ai', '获取资源失败，请稍后重试。');
    }
}

// 获取实践项目
async function getPracticeProjects() {
    addMessage('ai', '正在为您推荐合适的实践项目...');
    
    try {
        const response = await fetch(`${API_BASE_URL}/api/get-projects`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                domain: userProfile.domain,
                phase: userProfile.currentPhase,
                background: userProfile.background
            })
        });
        
        if (response.ok) {
            const data = await response.json();
            const projectsHtml = data.projects.map(p => 
                `<li><strong>${p.name}</strong><br>难度：${p.difficulty}<br>${p.description}</li>`
            ).join('');
            
            addMessage('ai', `
                <strong>推荐的实践项目：</strong>
                <ul>${projectsHtml}</ul>
                选择一个开始吧！我会指导您完成整个过程。
            `);
        }
    } catch (error) {
        console.error('Error:', error);
        addMessage('ai', '获取项目推荐失败，请稍后重试。');
    }
}

// 周度复盘
async function weeklyReview() {
    const weekNumber = Math.floor((new Date() - userProfile.startDate) / (7 * 24 * 60 * 60 * 1000)) + 1;
    addMessage('ai', `正在生成第${weekNumber}周的学习复盘...`);
    
    try {
        const response = await fetch(`${API_BASE_URL}/api/weekly-review`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                domain: userProfile.domain,
                week: weekNumber,
                progress: userProfile.progress,
                phase: userProfile.currentPhase
            })
        });
        
        if (response.ok) {
            const data = await response.json();
            addMessage('ai', data.review);
        }
    } catch (error) {
        console.error('Error:', error);
        addMessage('ai', '生成复盘失败，请稍后重试。');
    }
}

// 显示通知
function showNotification(message, type = 'info') {
    // 简单的通知实现
    alert(message);
}

// 初始化
document.addEventListener('DOMContentLoaded', () => {
    // 检查是否有保存的进度
    const savedProfile = localStorage.getItem('userProfile');
    if (savedProfile) {
        userProfile = JSON.parse(savedProfile);
        userProfile.startDate = new Date(userProfile.startDate);
        
        if (userProfile.domain) {
            setupSection.classList.remove('active');
            dashboardSection.classList.add('active');
            initializeDashboard();
        }
    }
});

// 保存进度
window.addEventListener('beforeunload', () => {
    localStorage.setItem('userProfile', JSON.stringify(userProfile));
});