// script.js

// 全局变量
let userProfile = {
    domain: '',
    background: '',
    startDate: null,
    currentPhase: 0,
    progress: 0
};

// API配置
const API_BASE_URL = 'http://localhost:8000';

// 当前选中的AI模型
let currentModel = 'deepseek';

// 流式显示状态
let isStreaming = false;

// 学习阶段定义
const learningPhases = [
    {
        name: '全局认知阶段',
        duration: 14,
        description: '完成领域经典书籍快速阅读，生成知识结构图谱',
        tasks: ['阅读3本核心书籍', '制作思维导图', '确定学习路径']
    },
    {
        name: '信息浸泡阶段',
        duration: 168,
        description: '持续关注行业动态，建立信息获取渠道',
        tasks: ['订阅10个专业资源', '每日浏览30分钟', '收集100个案例']
    },
    {
        name: '实践起步阶段',
        duration: 14,
        description: '选择简单项目开始实践，积累经验',
        tasks: ['完成1个小项目', '记录所有问题', '形成解决方案库']
    },
    {
        name: '交流提升阶段',
        duration: 28,
        description: '与专家交流，参与社群讨论',
        tasks: ['联系3位专家', '加入2个专业社群', '发表5篇见解']
    },
    {
        name: '跨界融合阶段',
        duration: 56,
        description: '结合原有专长，创造独特价值',
        tasks: ['找到3个结合点', '开发独特方法', '创建特色作品']
    },
    {
        name: '系统优化阶段',
        duration: 56,
        description: '补充短板，建立知识体系',
        tasks: ['识别薄弱环节', '建立知识库', '形成学习循环']
    }
];

// DOM元素
const setupSection = document.getElementById('setup-section');
const dashboardSection = document.getElementById('dashboard-section');
const domainInput = document.getElementById('domain-input');
const backgroundInput = document.getElementById('background-input');
const startBtn = document.getElementById('start-btn');
const progressCircle = document.getElementById('progress-circle');
const progressPercent = document.getElementById('progress-percent');
const currentPhaseEl = document.getElementById('current-phase');
const daysElapsedEl = document.getElementById('days-elapsed');
const learningDomainEl = document.getElementById('learning-domain');
const phasesList = document.getElementById('phases-list');
const phaseDetails = document.getElementById('phase-details');
const phaseDetailsContent = document.getElementById('phase-details-content');
const backToPhases = document.getElementById('back-to-phases');
const chatMessages = document.getElementById('chat-messages');
const chatInput = document.getElementById('chat-input');
const sendBtn = document.getElementById('send-btn');
const mindmapModal = document.getElementById('mindmap-modal');
const modelSelect = document.getElementById('ai-model-select');

// 事件监听器
startBtn.addEventListener('click', startLearning);
sendBtn.addEventListener('click', sendMessage);
chatInput.addEventListener('keypress', (e) => {
    if (e.key === 'Enter' && !e.shiftKey) {
        e.preventDefault();
        sendMessage();
    }
});

// 模型选择器
modelSelect.addEventListener('change', (e) => {
    currentModel = e.target.value;
    showNotification(`已切换到 ${e.target.options[e.target.selectedIndex].text}`, 'info');
});

// 阶段返回按钮
backToPhases.addEventListener('click', () => {
    phaseDetails.style.display = 'none';
    phasesList.style.display = 'grid';
});

// 快速操作按钮
document.getElementById('generate-mindmap-btn').addEventListener('click', generateMindmap);
document.getElementById('get-resources-btn').addEventListener('click', getResources);
document.getElementById('practice-project-btn').addEventListener('click', getPracticeProjects);
document.getElementById('weekly-review-btn').addEventListener('click', weeklyReview);

// 模态框关闭
document.querySelector('.close').addEventListener('click', () => {
    mindmapModal.style.display = 'none';
});

// 开始学习
async function startLearning() {
    const domain = domainInput.value.trim();
    const background = backgroundInput.value.trim();

    if (!domain) {
        showNotification('请输入您想学习的领域', 'error');
        return;
    }

    userProfile.domain = domain;
    userProfile.background = background;
    userProfile.startDate = new Date();

    // 显示加载状态
    startBtn.textContent = '生成中...';
    startBtn.disabled = true;

    try {
        // 调用API生成学习计划
        const response = await fetch(`${API_BASE_URL}/api/generate-plan`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                domain: domain,
                background: background
            })
        });

        if (response.ok) {
            const data = await response.json();
            // 切换到仪表板
            setupSection.classList.remove('active');
            dashboardSection.classList.add('active');

            // 初始化仪表板
            initializeDashboard();

            // 显示AI欢迎消息
            addMessage('ai', data.welcome_message || `太棒了！让我们开始您的${domain}专家之旅。我会全程陪伴并指导您的学习。`);
        }
    } catch (error) {
        console.error('Error:', error);
        showNotification('生成学习计划时出错，请重试', 'error');
    } finally {
        startBtn.textContent = '生成学习计划';
        startBtn.disabled = false;
    }
}

// 初始化仪表板
function initializeDashboard() {
    learningDomainEl.textContent = userProfile.domain;
    updateProgress();
    renderPhases();

    // 定时更新进度
    setInterval(updateProgress, 60000); // 每分钟更新一次
}

// 更新进度
function updateProgress() {
    if (!userProfile.startDate) return;

    const now = new Date();
    const elapsed = Math.floor((now - userProfile.startDate) / (1000 * 60 * 60 * 24)); // 天数
    const totalDays = 180; // 6个月
    const progress = Math.min((elapsed / totalDays) * 100, 100);

    userProfile.progress = progress;
    daysElapsedEl.textContent = `第 ${elapsed} 天`;

    // 更新进度环 (适应新的SVG尺寸)
    const circumference = 2 * Math.PI * 65; // 新的半径是65
    const offset = circumference - (progress / 100) * circumference;
    progressCircle.style.strokeDashoffset = offset;
    progressPercent.textContent = `${Math.round(progress)}%`;

    // 更新当前阶段
    let totalPhaseDays = 0;
    for (let i = 0; i < learningPhases.length; i++) {
        totalPhaseDays += learningPhases[i].duration;
        if (elapsed <= totalPhaseDays) {
            userProfile.currentPhase = i;
            currentPhaseEl.textContent = learningPhases[i].name;
            break;
        }
    }

    // 更新阶段卡片状态
    updatePhaseCards();
}

// 渲染学习阶段
function renderPhases() {
    phasesList.innerHTML = '';

    learningPhases.forEach((phase, index) => {
        const phaseCard = document.createElement('div');
        phaseCard.className = 'phase-card';
        phaseCard.dataset.phase = index;

        phaseCard.innerHTML = `
            <div class="phase-header">
                <span class="phase-name">${phase.name}</span>
                <span class="phase-status">预计${phase.duration}天</span>
            </div>
            <div class="phase-description">${phase.description}</div>
        `;

        phaseCard.addEventListener('click', () => showPhaseTasks(phase, index));
        phasesList.appendChild(phaseCard);
    });

    updatePhaseCards();
}

// 更新阶段卡片状态
function updatePhaseCards() {
    const cards = document.querySelectorAll('.phase-card');
    cards.forEach((card, index) => {
        card.classList.remove('active', 'completed');
        if (index < userProfile.currentPhase) {
            card.classList.add('completed');
        } else if (index === userProfile.currentPhase) {
            card.classList.add('active');
        }
    });
}

// 显示阶段任务
function showPhaseTasks(phase, phaseIndex) {
    // 切换到阶段详情视图
    phasesList.style.display = 'none';
    phaseDetails.style.display = 'block';

    // 生成阶段详情内容
    const tasksHtml = phase.tasks.map(task => `<li>${task}</li>`).join('');
    const progressInfo = getPhaseProgress(phaseIndex);

    phaseDetailsContent.innerHTML = `
        <div class="phase-detail-header">
            <h4>${phase.name}</h4>
            <div class="phase-progress-info">
                <span class="phase-duration">预计时间: ${phase.duration}天</span>
                <span class="phase-status-badge ${progressInfo.status}">${progressInfo.statusText}</span>
            </div>
        </div>
        <div class="phase-description-detail">
            <p>${phase.description}</p>
        </div>
        <div class="phase-tasks-section">
            <h5>关键任务：</h5>
            <ul class="phase-task-list">${tasksHtml}</ul>
        </div>
        <div class="phase-actions">
            <button class="phase-action-btn" onclick="askPhaseGuidance('${phase.name}')">
                获取具体指导
            </button>
            <button class="phase-action-btn" onclick="getPhaseResources(${phaseIndex})">
                获取相关资源
            </button>
        </div>
    `;
}

// 获取阶段进度信息
function getPhaseProgress(phaseIndex) {
    if (phaseIndex < userProfile.currentPhase) {
        return { status: 'completed', statusText: '已完成' };
    } else if (phaseIndex === userProfile.currentPhase) {
        return { status: 'active', statusText: '进行中' };
    } else {
        return { status: 'pending', statusText: '未开始' };
    }
}

// 询问阶段指导
function askPhaseGuidance(phaseName) {
    const message = `请为我提供${phaseName}的具体学习指导和建议`;
    chatInput.value = message;
    sendMessage();
}

// 获取阶段资源
function getPhaseResources(phaseIndex) {
    getResources(phaseIndex);
}

// 发送消息
async function sendMessage() {
    const message = chatInput.value.trim();
    if (!message || isStreaming) return;

    // 添加用户消息
    addMessage('user', message);
    chatInput.value = '';

    // 禁用发送按钮
    sendBtn.disabled = true;
    sendBtn.textContent = '发送中...';
    isStreaming = true;

    // 添加思考标签
    const thinkingId = addThinkingMessage();

    try {
        const response = await fetch(`${API_BASE_URL}/api/chat`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                message: message,
                domain: userProfile.domain,
                phase: learningPhases[userProfile.currentPhase].name,
                context: userProfile,
                model: currentModel
            })
        });

        if (response.ok) {
            const data = await response.json();

            // 隐藏思考标签
            hideThinkingMessage(thinkingId);

            // 流式显示AI回复
            await streamMessage(data.response);
        } else {
            hideThinkingMessage(thinkingId);
            addMessage('ai', '抱歉，服务器出现错误。请稍后重试。');
        }
    } catch (error) {
        console.error('Error:', error);
        hideThinkingMessage(thinkingId);
        addMessage('ai', '抱歉，网络连接出现问题。请检查网络后重试。');
    } finally {
        // 恢复发送按钮
        sendBtn.disabled = false;
        sendBtn.textContent = '发送';
        isStreaming = false;
    }
}

// 添加消息到聊天框
function addMessage(type, content) {
    const messageDiv = document.createElement('div');
    messageDiv.className = `message ${type}`;
    const messageId = Date.now();
    messageDiv.id = `msg-${messageId}`;

    // 如果是AI消息，支持Markdown渲染
    const processedContent = type === 'ai' ? renderMarkdown(content) : content;

    messageDiv.innerHTML = `
        <div class="message-bubble">${processedContent}</div>
    `;

    chatMessages.appendChild(messageDiv);
    chatMessages.scrollTop = chatMessages.scrollHeight;

    return messageId;
}

// 更新消息
function updateMessage(messageId, newContent) {
    const messageEl = document.getElementById(`msg-${messageId}`);
    if (messageEl) {
        const processedContent = renderMarkdown(newContent);
        messageEl.querySelector('.message-bubble').innerHTML = processedContent;
    }
}

// 添加思考标签
function addThinkingMessage() {
    const messageDiv = document.createElement('div');
    messageDiv.className = 'message ai';
    const messageId = Date.now();
    messageDiv.id = `thinking-${messageId}`;

    messageDiv.innerHTML = `
        <div class="message-bubble">
            <div class="thinking-tag collapsed" onclick="toggleThinking(${messageId})">
                🤔 AI正在思考...
            </div>
            <div class="thinking-content hidden" id="thinking-content-${messageId}">
                正在分析您的问题，结合当前学习阶段和领域特点，为您提供最优答案...
            </div>
        </div>
    `;

    chatMessages.appendChild(messageDiv);
    chatMessages.scrollTop = chatMessages.scrollHeight;

    return messageId;
}

// 隐藏思考标签
function hideThinkingMessage(messageId) {
    const messageEl = document.getElementById(`thinking-${messageId}`);
    if (messageEl) {
        messageEl.remove();
    }
}

// 切换思考内容显示
function toggleThinking(messageId) {
    const thinkingTag = document.querySelector(`#thinking-${messageId} .thinking-tag`);
    const thinkingContent = document.getElementById(`thinking-content-${messageId}`);

    if (thinkingContent.classList.contains('hidden')) {
        thinkingContent.classList.remove('hidden');
        thinkingTag.classList.remove('collapsed');
        thinkingTag.classList.add('expanded');
    } else {
        thinkingContent.classList.add('hidden');
        thinkingTag.classList.remove('expanded');
        thinkingTag.classList.add('collapsed');
    }
}

// 流式显示消息
async function streamMessage(content) {
    const messageId = addMessage('ai', '');
    const messageBubble = document.querySelector(`#msg-${messageId} .message-bubble`);
    messageBubble.classList.add('streaming');

    // 模拟流式输出
    const words = content.split('');
    let currentContent = '';

    for (let i = 0; i < words.length; i++) {
        currentContent += words[i];
        messageBubble.innerHTML = renderMarkdown(currentContent);
        chatMessages.scrollTop = chatMessages.scrollHeight;

        // 模拟打字效果
        await new Promise(resolve => setTimeout(resolve, 20));
    }

    messageBubble.classList.remove('streaming');
}

// 简单的Markdown渲染器
function renderMarkdown(text) {
    if (!text) return '';

    return text
        // 粗体
        .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
        // 斜体
        .replace(/\*(.*?)\*/g, '<em>$1</em>')
        // 代码块
        .replace(/```([\s\S]*?)```/g, '<pre><code>$1</code></pre>')
        // 行内代码
        .replace(/`(.*?)`/g, '<code>$1</code>')
        // 标题
        .replace(/^### (.*$)/gm, '<h3>$1</h3>')
        .replace(/^## (.*$)/gm, '<h2>$1</h2>')
        .replace(/^# (.*$)/gm, '<h1>$1</h1>')
        // 列表
        .replace(/^\* (.*$)/gm, '<li>$1</li>')
        .replace(/^- (.*$)/gm, '<li>$1</li>')
        // 换行
        .replace(/\n/g, '<br>');
}

// 生成知识图谱
async function generateMindmap() {
    mindmapModal.style.display = 'block';
    const mindmapContainer = document.getElementById('mindmap-container');
    mindmapContainer.innerHTML = '<p>正在生成知识图谱...</p>';

    try {
        const response = await fetch(`${API_BASE_URL}/api/generate-mindmap`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                domain: userProfile.domain,
                phase: userProfile.currentPhase,
                model: currentModel
            })
        });

        if (response.ok) {
            const data = await response.json();
            displayMindmap(data.mindmap);
        } else {
            mindmapContainer.innerHTML = '<p>生成知识图谱失败，请重试</p>';
        }
    } catch (error) {
        console.error('Error:', error);
        mindmapContainer.innerHTML = '<p>网络错误，请检查网络连接</p>';
    }
}

// 显示知识图谱
function displayMindmap(mindmapData) {
    const container = document.getElementById('mindmap-container');

    // 检查数据格式
    if (mindmapData.raw_text) {
        // 如果是原始文本，显示结构化内容
        container.innerHTML = `
            <div class="mindmap-content">
                <h3>${userProfile.domain}知识结构</h3>
                <div class="mindmap-text">${renderMarkdown(mindmapData.raw_text)}</div>
            </div>
        `;
    } else if (mindmapData.core_concepts) {
        // 如果是结构化数据，生成可视化图谱
        generateVisualMindmap(container, mindmapData);
    } else {
        // 备用显示
        container.innerHTML = `
            <div class="mindmap-content">
                <h3>${userProfile.domain}知识结构</h3>
                <pre>${JSON.stringify(mindmapData, null, 2)}</pre>
            </div>
        `;
    }
}

// 生成可视化知识图谱
function generateVisualMindmap(container, data) {
    container.innerHTML = `
        <div class="mindmap-content">
            <h3>${data.domain || userProfile.domain}知识结构</h3>
            <div class="mindmap-visualization" id="mindmap-viz"></div>
        </div>
    `;

    const vizContainer = document.getElementById('mindmap-viz');

    // 清空容器
    vizContainer.innerHTML = '';

    // 中心节点
    const centerNode = createMindmapNode(
        data.domain || userProfile.domain,
        200, 200,
        'root'
    );
    vizContainer.appendChild(centerNode);

    // 添加核心概念节点
    if (data.core_concepts && data.core_concepts.length > 0) {
        const angleStep = (2 * Math.PI) / data.core_concepts.length;
        const radius = 120;

        data.core_concepts.forEach((concept, index) => {
            const angle = index * angleStep;
            const x = 200 + radius * Math.cos(angle);
            const y = 200 + radius * Math.sin(angle);

            // 创建概念节点
            const conceptNode = createMindmapNode(
                concept.name || concept,
                x, y,
                'level-1'
            );
            vizContainer.appendChild(conceptNode);

            // 创建连接线
            const connection = createConnection(200, 200, x, y);
            vizContainer.appendChild(connection);

            // 添加子主题
            if (concept.subtopics && concept.subtopics.length > 0) {
                const subRadius = 80;
                const subAngleStep = Math.PI / (concept.subtopics.length + 1);
                const baseAngle = angle - Math.PI / 2;

                concept.subtopics.forEach((subtopic, subIndex) => {
                    const subAngle = baseAngle + (subIndex + 1) * subAngleStep;
                    const subX = x + subRadius * Math.cos(subAngle);
                    const subY = y + subRadius * Math.sin(subAngle);

                    const subNode = createMindmapNode(
                        subtopic,
                        subX, subY,
                        'level-2'
                    );
                    vizContainer.appendChild(subNode);

                    const subConnection = createConnection(x, y, subX, subY);
                    vizContainer.appendChild(subConnection);
                });
            }
        });
    }
}

// 创建思维导图节点
function createMindmapNode(text, x, y, level) {
    const node = document.createElement('div');
    node.className = `mindmap-node ${level}`;
    node.style.left = `${x - 50}px`;
    node.style.top = `${y - 15}px`;
    node.textContent = text;

    // 添加点击事件
    node.addEventListener('click', () => {
        showNotification(`点击了节点: ${text}`, 'info');
    });

    return node;
}

// 创建连接线
function createConnection(x1, y1, x2, y2) {
    const connection = document.createElement('div');
    connection.className = 'mindmap-connection';

    const length = Math.sqrt((x2 - x1) ** 2 + (y2 - y1) ** 2);
    const angle = Math.atan2(y2 - y1, x2 - x1) * 180 / Math.PI;

    connection.style.width = `${length}px`;
    connection.style.left = `${x1}px`;
    connection.style.top = `${y1}px`;
    connection.style.transform = `rotate(${angle}deg)`;
    connection.style.transformOrigin = '0 50%';

    return connection;
}

// 获取学习资源
async function getResources(phaseIndex = null) {
    const thinkingId = addThinkingMessage();

    try {
        const response = await fetch(`${API_BASE_URL}/api/get-resources`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                domain: userProfile.domain,
                phase: phaseIndex !== null ? phaseIndex : userProfile.currentPhase,
                model: currentModel
            })
        });

        if (response.ok) {
            const data = await response.json();
            hideThinkingMessage(thinkingId);

            const resourcesHtml = data.resources.map(r =>
                `<li><strong>${r.title}</strong> - ${r.type}<br><small>${r.description}</small></li>`
            ).join('');

            await streamMessage(`
**为您推荐的学习资源：**

${data.resources.map(r => `- **${r.title}** (${r.type})\n  ${r.description}`).join('\n\n')}
            `);
        } else {
            hideThinkingMessage(thinkingId);
            addMessage('ai', '获取资源失败，请稍后重试。');
        }
    } catch (error) {
        console.error('Error:', error);
        hideThinkingMessage(thinkingId);
        addMessage('ai', '网络错误，请检查网络连接。');
    }
}

// 获取实践项目
async function getPracticeProjects() {
    const thinkingId = addThinkingMessage();

    try {
        const response = await fetch(`${API_BASE_URL}/api/get-projects`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                domain: userProfile.domain,
                phase: userProfile.currentPhase,
                background: userProfile.background,
                model: currentModel
            })
        });

        if (response.ok) {
            const data = await response.json();
            hideThinkingMessage(thinkingId);

            await streamMessage(`
**推荐的实践项目：**

${data.projects.map((p, index) => `${index + 1}. **${p.name}** (难度: ${p.difficulty})\n   ${p.description}`).join('\n\n')}

选择一个开始吧！我会指导您完成整个过程。
            `);
        } else {
            hideThinkingMessage(thinkingId);
            addMessage('ai', '获取项目推荐失败，请稍后重试。');
        }
    } catch (error) {
        console.error('Error:', error);
        hideThinkingMessage(thinkingId);
        addMessage('ai', '网络错误，请检查网络连接。');
    }
}

// 周度复盘
async function weeklyReview() {
    const weekNumber = Math.floor((new Date() - userProfile.startDate) / (7 * 24 * 60 * 60 * 1000)) + 1;
    const thinkingId = addThinkingMessage();

    try {
        const response = await fetch(`${API_BASE_URL}/api/weekly-review`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                domain: userProfile.domain,
                week: weekNumber,
                progress: userProfile.progress,
                phase: userProfile.currentPhase,
                model: currentModel
            })
        });

        if (response.ok) {
            const data = await response.json();
            hideThinkingMessage(thinkingId);
            await streamMessage(data.review);
        } else {
            hideThinkingMessage(thinkingId);
            addMessage('ai', '生成复盘失败，请稍后重试。');
        }
    } catch (error) {
        console.error('Error:', error);
        hideThinkingMessage(thinkingId);
        addMessage('ai', '网络错误，请检查网络连接。');
    }
}

// 显示通知
function showNotification(message, type = 'info') {
    // 简单的通知实现
    alert(message);
}

// 初始化
document.addEventListener('DOMContentLoaded', () => {
    // 检查是否有保存的进度
    const savedProfile = localStorage.getItem('userProfile');
    if (savedProfile) {
        userProfile = JSON.parse(savedProfile);
        userProfile.startDate = new Date(userProfile.startDate);

        if (userProfile.domain) {
            setupSection.classList.remove('active');
            dashboardSection.classList.add('active');
            initializeDashboard();
        }
    }
});

// 保存进度
window.addEventListener('beforeunload', () => {
    localStorage.setItem('userProfile', JSON.stringify(userProfile));
});