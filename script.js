// script.js

// 全局变量
let userProfile = {
    domain: '',
    background: '',
    startDate: null,
    currentPhase: 0,
    progress: 0
};

// API配置
const API_BASE_URL = 'http://localhost:8000';

// 当前选中的AI模型
let currentModel = 'deepseek';

// 流式显示状态
let isStreaming = false;

// 学习阶段定义
const learningPhases = [
    {
        name: '全局认知阶段',
        duration: 14,
        description: '完成领域经典书籍快速阅读，生成知识结构图谱',
        tasks: ['阅读3本核心书籍', '制作思维导图', '确定学习路径']
    },
    {
        name: '信息浸泡阶段',
        duration: 60,  // 缩短为60天
        description: '持续关注行业动态，建立信息获取渠道',
        tasks: ['订阅10个专业资源', '每日浏览30分钟', '收集100个案例']
    },
    {
        name: '实践起步阶段',
        duration: 21,  // 延长为21天
        description: '选择简单项目开始实践，积累经验',
        tasks: ['完成1个小项目', '记录所有问题', '形成解决方案库']
    },
    {
        name: '交流提升阶段',
        duration: 35,  // 延长为35天
        description: '与专家交流，参与社群讨论',
        tasks: ['联系3位专家', '加入2个专业社群', '发表5篇见解']
    },
    {
        name: '跨界融合阶段',
        duration: 42,  // 缩短为42天
        description: '结合原有专长，创造独特价值',
        tasks: ['找到3个结合点', '开发独特方法', '创建特色作品']
    },
    {
        name: '系统优化阶段',
        duration: 28,  // 缩短为28天
        description: '补充短板，建立知识体系',
        tasks: ['识别薄弱环节', '建立知识库', '形成学习循环']
    }
];

// 阶段切换要求定义
const phaseRequirements = [
    {
        name: '全局认知阶段',
        minDays: 7,
        maxDays: 21,
        requiredActivities: {
            chatCount: 15,
            mindmapGenerated: 2,
            resourcesViewed: 3,
            resourceUrlClicks: 5  // 新增：点击URL 5次
        }
    },
    {
        name: '信息浸泡阶段',
        minDays: 30,
        maxDays: 90,
        requiredActivities: {
            chatCount: 50,
            resourcesViewed: 10,
            reviewsCompleted: 2
        }
    },
    {
        name: '实践起步阶段',
        minDays: 14,
        maxDays: 35,
        requiredActivities: {
            chatCount: 30,
            projectsViewed: 5,
            mindmapGenerated: 1
        }
    },
    {
        name: '交流提升阶段',
        minDays: 21,
        maxDays: 49,
        requiredActivities: {
            chatCount: 40,
            reviewsCompleted: 3,
            resourcesViewed: 8
        }
    },
    {
        name: '跨界融合阶段',
        minDays: 28,
        maxDays: 56,
        requiredActivities: {
            chatCount: 60,
            mindmapGenerated: 3,
            projectsViewed: 8
        }
    },
    {
        name: '系统优化阶段',
        minDays: 21,
        maxDays: 42,
        requiredActivities: {
            chatCount: 50,
            reviewsCompleted: 4,
            resourcesViewed: 12
        }
    }
];

// DOM元素
const setupSection = document.getElementById('setup-section');
const dashboardSection = document.getElementById('dashboard-section');
const domainInput = document.getElementById('domain-input');
const backgroundInput = document.getElementById('background-input');
const startBtn = document.getElementById('start-btn');
const progressCircle = document.getElementById('progress-circle');
const progressPercent = document.getElementById('progress-percent');
const currentPhaseEl = document.getElementById('current-phase');
const daysElapsedEl = document.getElementById('days-elapsed');
const learningDomainEl = document.getElementById('learning-domain');
const phasesList = document.getElementById('phases-list');
const phaseDetails = document.getElementById('phase-details');
const phaseDetailsContent = document.getElementById('phase-details-content');
const backToPhases = document.getElementById('back-to-phases');
const chatMessages = document.getElementById('chat-messages');
const chatInput = document.getElementById('chat-input');
const sendBtn = document.getElementById('send-btn');
const mindmapModal = document.getElementById('mindmap-modal');
const modelSelect = document.getElementById('ai-model-select');

// 事件监听器
startBtn.addEventListener('click', startLearning);
sendBtn.addEventListener('click', sendMessage);
chatInput.addEventListener('keypress', (e) => {
    if (e.key === 'Enter' && !e.shiftKey) {
        e.preventDefault();
        sendMessage();
    }
});

// 模型选择器
modelSelect.addEventListener('change', (e) => {
    currentModel = e.target.value;
    showNotification(`已切换到 ${e.target.options[e.target.selectedIndex].text}`, 'info');
});

// 阶段返回按钮
backToPhases.addEventListener('click', () => {
    phaseDetails.style.display = 'none';
    phasesList.style.display = 'grid';
});

// 快速操作按钮
document.getElementById('generate-mindmap-btn').addEventListener('click', generateMindmap);
document.getElementById('get-resources-btn').addEventListener('click', getResources);
document.getElementById('practice-project-btn').addEventListener('click', getPracticeProjects);
document.getElementById('weekly-review-btn').addEventListener('click', weeklyReview);

// 模态框关闭
document.querySelector('.close').addEventListener('click', () => {
    mindmapModal.style.display = 'none';
});

// 开始学习
async function startLearning() {
    const domain = domainInput.value.trim();
    const background = backgroundInput.value.trim();

    if (!domain) {
        showNotification('请输入您想学习的领域', 'error');
        return;
    }

    userProfile.domain = domain;
    userProfile.background = background;
    userProfile.startDate = new Date();

    // 显示加载状态
    startBtn.textContent = '生成中...';
    startBtn.disabled = true;

    try {
        // 调用API生成学习计划
        const response = await fetch(`${API_BASE_URL}/api/generate-plan`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                domain: domain,
                background: background
            })
        });

        if (response.ok) {
            const data = await response.json();
            // 切换到仪表板
            setupSection.classList.remove('active');
            dashboardSection.classList.add('active');

            // 初始化仪表板
            initializeDashboard();

            // 显示AI欢迎消息
            addMessage('ai', data.welcome_message || `太棒了！让我们开始您的${domain}专家之旅。我会全程陪伴并指导您的学习。`);
        }
    } catch (error) {
        console.error('Error:', error);
        showNotification('生成学习计划时出错，请重试', 'error');
    } finally {
        startBtn.textContent = '生成学习计划';
        startBtn.disabled = false;
    }
}

// 初始化仪表板
function initializeDashboard() {
    learningDomainEl.textContent = userProfile.domain;
    updateProgress();
    renderPhases();

    // 定时更新进度
    setInterval(updateProgress, 60000); // 每分钟更新一次
}

// 学习活动记录
let learningActivities = {
    chatCount: 0,
    mindmapGenerated: 0,
    resourcesViewed: 0,
    projectsViewed: 0,
    reviewsCompleted: 0,
    resourceUrlClicks: 0,  // 新增：URL点击次数
    lastActivityDate: null
};

// 当前阶段活动记录
let currentPhaseActivities = {
    chatCount: 0,
    mindmapGenerated: 0,
    resourcesViewed: 0,
    projectsViewed: 0,
    reviewsCompleted: 0,
    resourceUrlClicks: 0,  // 新增：URL点击次数
    phaseStartDate: null
};

// 更新进度
function updateProgress() {
    if (!userProfile.startDate) return;

    const now = new Date();
    const elapsed = Math.floor((now - userProfile.startDate) / (1000 * 60 * 60 * 24)); // 天数
    const totalDays = 180; // 6个月

    // 基础时间进度
    let baseProgress = Math.min((elapsed / totalDays) * 100, 100);

    // 根据学习活动调整进度
    let activityBonus = calculateActivityBonus();
    let finalProgress = Math.min(baseProgress + activityBonus, 100);

    userProfile.progress = finalProgress;
    daysElapsedEl.textContent = `第 ${elapsed} 天`;

    // 更新进度环 (适应新的SVG尺寸)
    const circumference = 2 * Math.PI * 65; // 新的半径是65
    const offset = circumference - (finalProgress / 100) * circumference;
    progressCircle.style.strokeDashoffset = offset;
    progressPercent.textContent = `${Math.round(finalProgress)}%`;

    // 更新当前阶段（智能切换）
    const phaseAdvanceCheck = checkPhaseAdvancement(elapsed);
    if (phaseAdvanceCheck.shouldAdvance) {
        advanceToNextPhase(phaseAdvanceCheck.reason);
    } else {
        // 传统时间切换作为备用
        let totalPhaseDays = 0;
        for (let i = 0; i < learningPhases.length; i++) {
            totalPhaseDays += learningPhases[i].duration;
            if (elapsed <= totalPhaseDays) {
                if (userProfile.currentPhase !== i) {
                    userProfile.currentPhase = i;
                    currentPhaseEl.textContent = learningPhases[i].name;
                    resetCurrentPhaseActivities();
                }
                break;
            }
        }
    }

    // 更新阶段卡片状态
    updatePhaseCards();

    // 保存学习活动数据
    saveLearningActivities();
}

// 计算活动奖励进度
function calculateActivityBonus() {
    let bonus = 0;

    // 每次聊天增加0.1%进度
    bonus += learningActivities.chatCount * 0.1;

    // 生成知识图谱增加0.5%进度
    bonus += learningActivities.mindmapGenerated * 0.5;

    // 查看资源增加0.3%进度
    bonus += learningActivities.resourcesViewed * 0.3;

    // 查看项目增加0.3%进度
    bonus += learningActivities.projectsViewed * 0.3;

    // 完成复盘增加1%进度
    bonus += learningActivities.reviewsCompleted * 1.0;

    // 最大奖励进度不超过20%
    return Math.min(bonus, 20);
}

// 记录学习活动
function recordLearningActivity(activityType) {
    const now = new Date();
    learningActivities.lastActivityDate = now;

    // 更新总活动计数
    switch (activityType) {
        case 'chat':
            learningActivities.chatCount++;
            currentPhaseActivities.chatCount++;
            break;
        case 'mindmap':
            learningActivities.mindmapGenerated++;
            currentPhaseActivities.mindmapGenerated++;
            break;
        case 'resources':
            learningActivities.resourcesViewed++;
            currentPhaseActivities.resourcesViewed++;
            break;
        case 'projects':
            learningActivities.projectsViewed++;
            currentPhaseActivities.projectsViewed++;
            break;
        case 'review':
            learningActivities.reviewsCompleted++;
            currentPhaseActivities.reviewsCompleted++;
            break;
        case 'url-click':
            learningActivities.resourceUrlClicks++;
            currentPhaseActivities.resourceUrlClicks++;
            break;
    }

    // 立即更新进度
    updateProgress();

    // 显示进度提示
    const bonus = calculateActivityBonus();
    if (bonus > 0) {
        showProgressNotification(activityType, bonus);
    }

    // 检查是否可以进入下一阶段
    checkAndShowPhaseAdvanceHint();
}

// 检查阶段进展
function checkPhaseAdvancement(elapsedDays) {
    if (userProfile.currentPhase >= learningPhases.length - 1) {
        return { shouldAdvance: false, reason: '已是最后阶段' };
    }

    const currentPhaseIndex = userProfile.currentPhase;
    const requirements = phaseRequirements[currentPhaseIndex];
    const currentPhaseDays = getCurrentPhaseDays();

    // 检查最大时间限制（强制切换）
    if (currentPhaseDays >= requirements.maxDays) {
        return {
            shouldAdvance: true,
            reason: `时间到期（${requirements.maxDays}天），自动进入下一阶段`
        };
    }

    // 检查最小时间要求
    if (currentPhaseDays < requirements.minDays) {
        return { shouldAdvance: false, reason: '时间不足' };
    }

    // 检查学习活动要求
    const activityCheck = checkActivityRequirements(requirements.requiredActivities);
    if (activityCheck.allMet) {
        return {
            shouldAdvance: true,
            reason: '学习活动要求已满足，可以进入下一阶段'
        };
    }

    return { shouldAdvance: false, reason: '活动要求未满足' };
}

// 检查活动要求
function checkActivityRequirements(requirements) {
    const missing = [];
    let allMet = true;

    for (const [activity, required] of Object.entries(requirements)) {
        const current = currentPhaseActivities[activity] || 0;
        if (current < required) {
            allMet = false;
            missing.push({
                activity,
                required,
                current,
                remaining: required - current
            });
        }
    }

    return { allMet, missing };
}

// 获取当前阶段天数
function getCurrentPhaseDays() {
    if (!currentPhaseActivities.phaseStartDate) {
        return 0;
    }
    const now = new Date();
    return Math.floor((now - currentPhaseActivities.phaseStartDate) / (1000 * 60 * 60 * 24));
}

// 进入下一阶段
function advanceToNextPhase(reason) {
    if (userProfile.currentPhase >= learningPhases.length - 1) {
        return; // 已是最后阶段
    }

    const oldPhase = learningPhases[userProfile.currentPhase];
    userProfile.currentPhase++;
    const newPhase = learningPhases[userProfile.currentPhase];

    currentPhaseEl.textContent = newPhase.name;

    // 重置当前阶段活动
    resetCurrentPhaseActivities();

    // 显示阶段切换通知
    showPhaseAdvanceNotification(oldPhase, newPhase, reason);

    // 更新阶段卡片
    updatePhaseCards();
}

// 重置当前阶段活动
function resetCurrentPhaseActivities() {
    currentPhaseActivities = {
        chatCount: 0,
        mindmapGenerated: 0,
        resourcesViewed: 0,
        projectsViewed: 0,
        reviewsCompleted: 0,
        resourceUrlClicks: 0,  // 新增
        phaseStartDate: new Date()
    };
    saveCurrentPhaseActivities();
}

// 检查并显示阶段进展提示
function checkAndShowPhaseAdvanceHint() {
    if (userProfile.currentPhase >= learningPhases.length - 1) {
        return; // 已是最后阶段
    }

    const requirements = phaseRequirements[userProfile.currentPhase];
    const currentPhaseDays = getCurrentPhaseDays();
    const activityCheck = checkActivityRequirements(requirements.requiredActivities);

    // 如果满足最小时间且活动要求已满足
    if (currentPhaseDays >= requirements.minDays && activityCheck.allMet) {
        showPhaseReadyNotification();
    } else if (currentPhaseDays >= requirements.minDays) {
        // 时间足够但活动不足
        showPhaseProgressHint(activityCheck.missing);
    }
}

// 显示阶段切换通知
function showPhaseAdvanceNotification(oldPhase, newPhase, reason) {
    const message = `🎉 恭喜！您已从「${oldPhase.name}」进入「${newPhase.name}」\n\n切换原因：${reason}`;

    // 创建特殊的阶段切换通知
    createPhaseAdvanceToast(message);
}

// 显示阶段准备就绪通知
function showPhaseReadyNotification() {
    const nextPhase = learningPhases[userProfile.currentPhase + 1];
    const message = `✨ 您已完成当前阶段的所有要求！\n可以进入下一阶段：${nextPhase.name}`;

    createProgressToast(message);
}

// 显示阶段进度提示
function showPhaseProgressHint(missing) {
    if (missing.length === 0) return;

    const hints = missing.map(m => {
        const activityNames = {
            chatCount: 'AI对话',
            mindmapGenerated: '知识图谱',
            resourcesViewed: '学习资源',
            projectsViewed: '实践项目',
            reviewsCompleted: '周度复盘',
            resourceUrlClicks: '点击资源链接'
        };
        return `${activityNames[m.activity]}: ${m.current}/${m.required} (还需${m.remaining}次)`;
    }).join('\n');

    const message = `💯 阶段进展提示：\n${hints}`;

    // 使用不同颜色的提示
    createHintToast(message);
}

// 创建阶段切换Toast
function createPhaseAdvanceToast(message) {
    const toast = document.createElement('div');
    toast.className = 'phase-advance-toast';
    toast.textContent = message;

    toast.style.cssText = `
        position: fixed;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        background: linear-gradient(135deg, #4CAF50, #45a049);
        color: white;
        padding: 20px 30px;
        border-radius: 12px;
        box-shadow: 0 8px 24px rgba(0,0,0,0.2);
        z-index: 1001;
        font-size: 16px;
        font-weight: 600;
        text-align: center;
        max-width: 400px;
        animation: phaseAdvanceIn 0.5s ease-out;
    `;

    document.body.appendChild(toast);

    // 5秒后移除
    setTimeout(() => {
        toast.style.animation = 'phaseAdvanceOut 0.5s ease-out';
        setTimeout(() => toast.remove(), 500);
    }, 5000);
}

// 创建提示Toast
function createHintToast(message) {
    const existingHint = document.querySelector('.hint-toast');
    if (existingHint) {
        existingHint.remove();
    }

    const toast = document.createElement('div');
    toast.className = 'hint-toast';
    toast.textContent = message;

    toast.style.cssText = `
        position: fixed;
        top: 20px;
        left: 20px;
        background: #FF9800;
        color: white;
        padding: 12px 20px;
        border-radius: 8px;
        box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        z-index: 1000;
        font-size: 14px;
        font-weight: 500;
        animation: slideInLeft 0.3s ease-out;
        max-width: 300px;
        white-space: pre-line;
    `;

    document.body.appendChild(toast);

    // 4秒后移除
    setTimeout(() => {
        toast.style.animation = 'slideOutLeft 0.3s ease-out';
        setTimeout(() => toast.remove(), 300);
    }, 4000);
}

// 保存当前阶段活动数据
function saveCurrentPhaseActivities() {
    localStorage.setItem('currentPhaseActivities', JSON.stringify(currentPhaseActivities));
}

// 加载当前阶段活动数据
function loadCurrentPhaseActivities() {
    const saved = localStorage.getItem('currentPhaseActivities');
    if (saved) {
        const data = JSON.parse(saved);
        currentPhaseActivities = {
            ...currentPhaseActivities,
            ...data,
            phaseStartDate: data.phaseStartDate ? new Date(data.phaseStartDate) : new Date()
        };
    } else {
        // 如果没有保存的数据，初始化为当前时间
        currentPhaseActivities.phaseStartDate = new Date();
        saveCurrentPhaseActivities();
    }
}

// 显示进度通知
function showProgressNotification(activityType, totalBonus) {
    const activityNames = {
        'chat': '与AI对话',
        'mindmap': '生成知识图谱',
        'resources': '查看学习资源',
        'projects': '查看实践项目',
        'review': '完成周度复盘',
        'url-click': '点击学习资源链接'
    };

    const message = `${activityNames[activityType]}完成！当前活动奖励进度: +${totalBonus.toFixed(1)}%`;

    // 创建更好的通知显示
    createProgressToast(message);
}

// 创建进度提示Toast
function createProgressToast(message) {
    // 移除现有的toast
    const existingToast = document.querySelector('.progress-toast');
    if (existingToast) {
        existingToast.remove();
    }

    // 创建新的toast
    const toast = document.createElement('div');
    toast.className = 'progress-toast';
    toast.textContent = message;

    // 添加样式
    toast.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background: #4CAF50;
        color: white;
        padding: 12px 20px;
        border-radius: 8px;
        box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        z-index: 1000;
        font-size: 14px;
        font-weight: 500;
        animation: slideInRight 0.3s ease-out;
    `;

    document.body.appendChild(toast);

    // 3秒后自动移除
    setTimeout(() => {
        toast.style.animation = 'slideOutRight 0.3s ease-out';
        setTimeout(() => toast.remove(), 300);
    }, 3000);
}

// 保存学习活动数据
function saveLearningActivities() {
    localStorage.setItem('learningActivities', JSON.stringify(learningActivities));
}

// 加载学习活动数据
function loadLearningActivities() {
    const saved = localStorage.getItem('learningActivities');
    if (saved) {
        learningActivities = { ...learningActivities, ...JSON.parse(saved) };
    }
}

// 渲染学习阶段
function renderPhases() {
    phasesList.innerHTML = '';

    learningPhases.forEach((phase, index) => {
        const phaseCard = document.createElement('div');
        phaseCard.className = 'phase-card';
        phaseCard.dataset.phase = index;

        phaseCard.innerHTML = `
            <div class="phase-header">
                <span class="phase-name">${phase.name}</span>
                <span class="phase-status">预计${phase.duration}天</span>
            </div>
            <div class="phase-description">${phase.description}</div>
        `;

        phaseCard.addEventListener('click', () => showPhaseTasks(phase, index));
        phasesList.appendChild(phaseCard);
    });

    updatePhaseCards();
}

// 更新阶段卡片状态
function updatePhaseCards() {
    const cards = document.querySelectorAll('.phase-card');
    cards.forEach((card, index) => {
        card.classList.remove('active', 'completed');
        if (index < userProfile.currentPhase) {
            card.classList.add('completed');
        } else if (index === userProfile.currentPhase) {
            card.classList.add('active');
        }
    });
}

// 显示阶段任务
function showPhaseTasks(phase, phaseIndex) {
    // 切换到阶段详情视图
    phasesList.style.display = 'none';
    phaseDetails.style.display = 'block';

    // 生成阶段详情内容
    const tasksHtml = phase.tasks.map(task => `<li>${task}</li>`).join('');
    const progressInfo = getPhaseProgress(phaseIndex);

    phaseDetailsContent.innerHTML = `
        <div class="phase-detail-header">
            <h4>${phase.name}</h4>
            <div class="phase-progress-info">
                <span class="phase-duration">预计时间: ${phase.duration}天</span>
                <span class="phase-status-badge ${progressInfo.status}">${progressInfo.statusText}</span>
            </div>
        </div>
        <div class="phase-description-detail">
            <p>${phase.description}</p>
        </div>
        <div class="phase-tasks-section">
            <h5>关键任务：</h5>
            <ul class="phase-task-list">${tasksHtml}</ul>
        </div>
        <div class="phase-actions">
            <button class="phase-action-btn" onclick="askPhaseGuidance('${phase.name}')">
                获取具体指导
            </button>
            <button class="phase-action-btn" onclick="getPhaseResources(${phaseIndex})">
                获取相关资源
            </button>
        </div>
    `;
}

// 获取阶段进度信息
function getPhaseProgress(phaseIndex) {
    if (phaseIndex < userProfile.currentPhase) {
        return { status: 'completed', statusText: '已完成' };
    } else if (phaseIndex === userProfile.currentPhase) {
        return { status: 'active', statusText: '进行中' };
    } else {
        return { status: 'pending', statusText: '未开始' };
    }
}

// 询问阶段指导
function askPhaseGuidance(phaseName) {
    const message = `请为我提供${phaseName}的具体学习指导和建议`;
    chatInput.value = message;
    sendMessage();
}

// 获取阶段资源
function getPhaseResources(phaseIndex) {
    getResources(phaseIndex);
}

// 发送消息
async function sendMessage() {
    const message = chatInput.value.trim();
    if (!message || isStreaming) return;

    // 添加用户消息
    addMessage('user', message);
    chatInput.value = '';

    // 禁用发送按钮
    sendBtn.disabled = true;
    sendBtn.textContent = '发送中...';
    isStreaming = true;

    // 添加思考标签
    const thinkingId = addThinkingMessage();

    try {
        const response = await fetch(`${API_BASE_URL}/api/chat`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                message: message,
                domain: userProfile.domain,
                phase: learningPhases[userProfile.currentPhase].name,
                context: userProfile,
                model: currentModel
            })
        });

        if (response.ok) {
            const data = await response.json();

            // 隐藏思考标签
            hideThinkingMessage(thinkingId);

            // 记录学习活动
            recordLearningActivity('chat');

            // 流式显示AI回复
            await streamMessage(data.response);
        } else {
            hideThinkingMessage(thinkingId);
            addMessage('ai', '抱歉，服务器出现错误。请稍后重试。');
        }
    } catch (error) {
        console.error('Error:', error);
        hideThinkingMessage(thinkingId);
        addMessage('ai', '抱歉，网络连接出现问题。请检查网络后重试。');
    } finally {
        // 恢复发送按钮
        sendBtn.disabled = false;
        sendBtn.textContent = '发送';
        isStreaming = false;
    }
}

// 添加消息到聊天框
function addMessage(type, content) {
    const messageDiv = document.createElement('div');
    messageDiv.className = `message ${type}`;
    const messageId = Date.now();
    messageDiv.id = `msg-${messageId}`;

    // 如果是AI消息，支持Markdown渲染
    const processedContent = type === 'ai' ? renderMarkdown(content) : content;

    messageDiv.innerHTML = `
        <div class="message-bubble">${processedContent}</div>
    `;

    chatMessages.appendChild(messageDiv);
    chatMessages.scrollTop = chatMessages.scrollHeight;

    return messageId;
}

// 添加HTML消息到聊天框
function addHtmlMessage(type, htmlContent) {
    const messageDiv = document.createElement('div');
    messageDiv.className = `message ${type}`;
    const messageId = Date.now();
    messageDiv.id = `msg-${messageId}`;

    messageDiv.innerHTML = `
        <div class="message-bubble">${htmlContent}</div>
    `;

    chatMessages.appendChild(messageDiv);
    chatMessages.scrollTop = chatMessages.scrollHeight;

    return messageId;
}

// 更新消息
function updateMessage(messageId, newContent) {
    const messageEl = document.getElementById(`msg-${messageId}`);
    if (messageEl) {
        const processedContent = renderMarkdown(newContent);
        messageEl.querySelector('.message-bubble').innerHTML = processedContent;
    }
}

// 添加思考标签
function addThinkingMessage() {
    const messageDiv = document.createElement('div');
    messageDiv.className = 'message ai';
    const messageId = Date.now();
    messageDiv.id = `thinking-${messageId}`;

    messageDiv.innerHTML = `
        <div class="message-bubble">
            <div class="thinking-tag collapsed" onclick="toggleThinking(${messageId})">
                🤔 AI正在思考...
            </div>
            <div class="thinking-content hidden" id="thinking-content-${messageId}">
                正在分析您的问题，结合当前学习阶段和领域特点，为您提供最优答案...
            </div>
        </div>
    `;

    chatMessages.appendChild(messageDiv);
    chatMessages.scrollTop = chatMessages.scrollHeight;

    return messageId;
}

// 隐藏思考标签
function hideThinkingMessage(messageId) {
    const messageEl = document.getElementById(`thinking-${messageId}`);
    if (messageEl) {
        messageEl.remove();
    }
}

// 切换思考内容显示
function toggleThinking(messageId) {
    const thinkingTag = document.querySelector(`#thinking-${messageId} .thinking-tag`);
    const thinkingContent = document.getElementById(`thinking-content-${messageId}`);

    if (thinkingContent.classList.contains('hidden')) {
        thinkingContent.classList.remove('hidden');
        thinkingTag.classList.remove('collapsed');
        thinkingTag.classList.add('expanded');
    } else {
        thinkingContent.classList.add('hidden');
        thinkingTag.classList.remove('expanded');
        thinkingTag.classList.add('collapsed');
    }
}

// 流式显示消息
async function streamMessage(content) {
    const messageId = addMessage('ai', '');
    const messageBubble = document.querySelector(`#msg-${messageId} .message-bubble`);
    messageBubble.classList.add('streaming');

    // 模拟流式输出
    const words = content.split('');
    let currentContent = '';

    for (let i = 0; i < words.length; i++) {
        currentContent += words[i];
        messageBubble.innerHTML = renderMarkdown(currentContent);
        chatMessages.scrollTop = chatMessages.scrollHeight;

        // 模拟打字效果
        await new Promise(resolve => setTimeout(resolve, 20));
    }

    messageBubble.classList.remove('streaming');
}

// 简单的Markdown渲染器
function renderMarkdown(text) {
    if (!text) return '';

    return text
        // 粗体
        .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
        // 斜体
        .replace(/\*(.*?)\*/g, '<em>$1</em>')
        // 代码块
        .replace(/```([\s\S]*?)```/g, '<pre><code>$1</code></pre>')
        // 行内代码
        .replace(/`(.*?)`/g, '<code>$1</code>')
        // 标题
        .replace(/^### (.*$)/gm, '<h3>$1</h3>')
        .replace(/^## (.*$)/gm, '<h2>$1</h2>')
        .replace(/^# (.*$)/gm, '<h1>$1</h1>')
        // 列表
        .replace(/^\* (.*$)/gm, '<li>$1</li>')
        .replace(/^- (.*$)/gm, '<li>$1</li>')
        // 换行
        .replace(/\n/g, '<br>');
}

// 生成知识图谱
async function generateMindmap() {
    mindmapModal.style.display = 'block';
    const mindmapContainer = document.getElementById('mindmap-container');
    mindmapContainer.innerHTML = '<p>正在生成知识图谱...</p>';

    try {
        const response = await fetch(`${API_BASE_URL}/api/generate-mindmap`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                domain: userProfile.domain,
                phase: userProfile.currentPhase,
                model: currentModel
            })
        });

        if (response.ok) {
            const data = await response.json();
            // 记录学习活动
            recordLearningActivity('mindmap');
            displayMindmap(data.mindmap);
        } else {
            mindmapContainer.innerHTML = '<p>生成知识图谱失败，请重试</p>';
        }
    } catch (error) {
        console.error('Error:', error);
        // 如果网络错误或API不可用，显示演示数据
        const demoData = createDemoMindmapData();
        displayMindmap(demoData);
    }
}

// 创建演示知识图谱数据
function createDemoMindmapData() {
    return {
        domain: userProfile.domain || 'Python编程',
        core_concepts: [
            {
                name: '基础语法',
                subtopics: ['变量与类型', '控制结构', '函数定义'],
                skills: ['编程思维', '代码调试']
            },
            {
                name: '数据结构',
                subtopics: ['列表元组', '字典集合', '字符串处理', '文件操作'],
                skills: ['数据处理', '算法思维']
            },
            {
                name: '面向对象',
                subtopics: ['类与对象', '继承多态'],
                skills: ['设计模式', '代码组织']
            },
            {
                name: '库与框架',
                subtopics: ['NumPy', 'Pandas', 'Flask', 'Django', 'Requests'],
                skills: ['库使用', '项目开发']
            },
            {
                name: '实际应用',
                subtopics: ['Web开发', '数据分析', '自动化', 'API开发'],
                skills: ['项目管理', '部署上线']
            }
        ],
        learning_path: ['语法基础', '数据操作', '面向对象', '库与框架', '实际项目']
    };
}

// 显示知识图谱
function displayMindmap(mindmapData) {
    const container = document.getElementById('mindmap-container');

    // 检查数据格式
    if (mindmapData.raw_text) {
        // 如果是原始文本，显示结构化内容
        container.innerHTML = `
            <div class="mindmap-content">
                <h3>${userProfile.domain}知识结构</h3>
                <div class="mindmap-text">${renderMarkdown(mindmapData.raw_text)}</div>
            </div>
        `;
    } else if (mindmapData.core_concepts) {
        // 如果是结构化数据，生成可视化图谱
        generateVisualMindmap(container, mindmapData);
    } else {
        // 备用显示
        container.innerHTML = `
            <div class="mindmap-content">
                <h3>${userProfile.domain}知识结构</h3>
                <pre>${JSON.stringify(mindmapData, null, 2)}</pre>
            </div>
        `;
    }
}

// 生成可视化知识图谱
function generateVisualMindmap(container, data) {
    container.innerHTML = `
        <div class="mindmap-content">
            <h3>${data.domain || userProfile.domain}知识结构</h3>
            <div class="mindmap-visualization" id="mindmap-viz">
                <svg class="mindmap-svg" width="100%" height="100%" viewBox="0 0 800 600">
                    <defs>
                        <marker id="arrowhead" markerWidth="10" markerHeight="7"
                                refX="9" refY="3.5" orient="auto">
                            <polygon points="0 0, 10 3.5, 0 7" fill="var(--primary-color)" opacity="0.6" />
                        </marker>
                    </defs>
                    <g id="connections"></g>
                    <g id="nodes"></g>
                </svg>
            </div>
        </div>
    `;

    const vizContainer = document.getElementById('mindmap-viz');
    const svg = vizContainer.querySelector('.mindmap-svg');
    const connectionsGroup = svg.querySelector('#connections');
    const nodesGroup = svg.querySelector('#nodes');

    // 计算中心位置
    const centerX = 400;
    const centerY = 300;

    // 添加拖拽功能
    addDragFunctionality(svg, connectionsGroup, nodesGroup);

    // 创建中心节点
    const centerNode = createMindmapNode(
        data.domain || userProfile.domain,
        centerX, centerY,
        'root'
    );
    nodesGroup.appendChild(centerNode);

    // 添加核心概念节点
    if (data.core_concepts && data.core_concepts.length > 0) {
        const conceptCount = data.core_concepts.length;
        const angleStep = (2 * Math.PI) / conceptCount;
        const radius = Math.min(150, 120 + conceptCount * 5); // 根据概念数量调整半径

        data.core_concepts.forEach((concept, index) => {
            const angle = index * angleStep - Math.PI / 2; // 从顶部开始
            const x = centerX + radius * Math.cos(angle);
            const y = centerY + radius * Math.sin(angle);

            // 创建概念节点
            const conceptNode = createMindmapNode(
                concept.name || concept,
                x, y,
                'level-1'
            );
            nodesGroup.appendChild(conceptNode);

            // 创建直线连接
            const connection = createStraightConnection(centerX, centerY, x, y, 'primary');
            connectionsGroup.appendChild(connection);

            // 添加子主题
            if (concept.subtopics && concept.subtopics.length > 0) {
                const subTopicCount = concept.subtopics.length;
                const subRadius = 120; // 增加子节点距离

                // 根据子主题数量动态调整扇形角度
                let fanAngle;
                if (subTopicCount <= 2) {
                    fanAngle = Math.PI / 4; // 45度
                } else if (subTopicCount <= 3) {
                    fanAngle = Math.PI / 2; // 90度
                } else {
                    fanAngle = Math.PI * 2 / 3; // 120度
                }

                const subAngleStep = fanAngle / Math.max(1, subTopicCount - 1);
                const startAngle = angle - fanAngle / 2;

                // 存储已使用的位置，避免重叠
                const usedPositions = [];

                concept.subtopics.forEach((subtopic, subIndex) => {
                    let subAngle = startAngle + subIndex * subAngleStep;
                    let subX = x + subRadius * Math.cos(subAngle);
                    let subY = y + subRadius * Math.sin(subAngle);

                    // 检查并避免与其他子节点重叠
                    let attempts = 0;
                    while (attempts < 10) {
                        let tooClose = false;
                        for (const pos of usedPositions) {
                            const distance = Math.sqrt((subX - pos.x) ** 2 + (subY - pos.y) ** 2);
                            if (distance < 80) { // 最小距离80像素
                                tooClose = true;
                                break;
                            }
                        }

                        if (!tooClose) break;

                        // 如果太近，调整角度
                        subAngle += Math.PI / 12; // 增加15度
                        subX = x + subRadius * Math.cos(subAngle);
                        subY = y + subRadius * Math.sin(subAngle);
                        attempts++;
                    }

                    // 确保子节点不超出边界，并留出足够边距
                    const nodeWidth = Math.max(80, subtopic.length * 8 + 20);
                    const margin = nodeWidth / 2 + 10;
                    const clampedX = Math.max(margin, Math.min(800 - margin, subX));
                    const clampedY = Math.max(30, Math.min(570, subY));

                    // 记录使用的位置
                    usedPositions.push({ x: clampedX, y: clampedY });

                    // 先创建连接线（在节点下层）
                    const subConnection = createStraightConnection(x, y, clampedX, clampedY, 'secondary');
                    connectionsGroup.appendChild(subConnection);

                    // 再创建节点（在连接线上层）
                    const subNode = createMindmapNode(
                        subtopic,
                        clampedX, clampedY,
                        'level-2'
                    );
                    nodesGroup.appendChild(subNode);
                });
            }
        });
    }
}

// 创建思维导图节点
function createMindmapNode(text, x, y, level) {
    // 创建SVG外部元素（foreignObject）
    const foreignObject = document.createElementNS('http://www.w3.org/2000/svg', 'foreignObject');

    // 根据节点类型计算尺寸，确保方框大于文字长度
    let nodeWidth, nodeHeight, fontSize;

    if (level === 'root') {
        // 根节点：最大尺寸
        nodeWidth = Math.max(120, text.length * 12 + 40);
        nodeHeight = 50;
        fontSize = '16px';
    } else if (level === 'level-1') {
        // 一级节点：中等尺寸
        nodeWidth = Math.max(100, text.length * 10 + 30);
        nodeHeight = 40;
        fontSize = '14px';
    } else {
        // 二级节点：较小尺寸
        nodeWidth = Math.max(80, text.length * 9 + 25);
        nodeHeight = 35;
        fontSize = '12px';
    }

    foreignObject.setAttribute('x', x - nodeWidth / 2);
    foreignObject.setAttribute('y', y - nodeHeight / 2);
    foreignObject.setAttribute('width', nodeWidth);
    foreignObject.setAttribute('height', nodeHeight);

    // 创建HTML元素
    const node = document.createElement('div');
    node.className = `mindmap-node ${level}`;
    node.style.width = '100%';
    node.style.height = '100%';
    node.style.display = 'flex';
    node.style.alignItems = 'center';
    node.style.justifyContent = 'center';
    node.style.position = 'relative';
    node.style.fontSize = fontSize;
    node.style.fontWeight = level === 'root' ? '600' : (level === 'level-1' ? '500' : '400');
    node.textContent = text;

    // 添加点击事件
    node.addEventListener('click', () => {
        showNotification(`点击了节点: ${text}`, 'info');
    });

    foreignObject.appendChild(node);
    return foreignObject;
}

// 创建直线连接
function createStraightConnection(x1, y1, x2, y2, type = 'primary') {
    const line = document.createElementNS('http://www.w3.org/2000/svg', 'line');

    line.setAttribute('x1', x1);
    line.setAttribute('y1', y1);
    line.setAttribute('x2', x2);
    line.setAttribute('y2', y2);
    line.setAttribute('stroke-width', type === 'secondary' ? '1.5' : '2');
    line.setAttribute('opacity', type === 'secondary' ? '0.6' : '0.7');
    line.setAttribute('stroke-linecap', 'round');

    // 根据类型设置颜色和样式
    if (type === 'primary') {
        line.setAttribute('stroke', '#4CAF50');
    } else if (type === 'secondary') {
        line.setAttribute('stroke', '#2196F3');
        line.setAttribute('stroke-dasharray', '5,3'); // 虚线效果
    } else {
        line.setAttribute('stroke', '#666666');
    }

    // 设置低于z-index，确保在节点下方
    line.style.zIndex = '-1';

    // 添加动画效果
    const distance = Math.sqrt((x2 - x1) ** 2 + (y2 - y1) ** 2);
    line.style.strokeDasharray = distance;
    line.style.strokeDashoffset = distance;
    line.style.animation = 'drawPath 1s ease-out forwards';
    line.style.animationDelay = type === 'secondary' ? '0.6s' : '0.3s';

    return line;
}

// 创建曲线连接（保留作为备用）
function createCurvedConnection(x1, y1, x2, y2, type = 'primary') {
    const path = document.createElementNS('http://www.w3.org/2000/svg', 'path');

    // 计算控制点以创建曲线
    const dx = x2 - x1;
    const dy = y2 - y1;
    const distance = Math.sqrt(dx * dx + dy * dy);

    // 调整曲率，使曲线更加平缓，减少与节点的重叠
    const curvature = type === 'secondary' ? 0.2 : 0.25; // 次级连接更平缓
    const offsetX = -dy * curvature;
    const offsetY = dx * curvature;

    // 使用更平缓的控制点分布
    const cp1x = x1 + dx * 0.25 + offsetX * 0.8;
    const cp1y = y1 + dy * 0.25 + offsetY * 0.8;
    const cp2x = x1 + dx * 0.75 + offsetX * 0.8;
    const cp2y = y1 + dy * 0.75 + offsetY * 0.8;

    // 创建贝塞尔曲线路径
    const pathData = `M ${x1} ${y1} C ${cp1x} ${cp1y}, ${cp2x} ${cp2y}, ${x2} ${y2}`;

    path.setAttribute('d', pathData);
    path.setAttribute('fill', 'none');
    path.setAttribute('stroke-width', type === 'secondary' ? '1.5' : '2');
    path.setAttribute('opacity', type === 'secondary' ? '0.6' : '0.7');

    // 根据类型设置颜色和样式
    if (type === 'primary') {
        path.setAttribute('stroke', '#4CAF50');
        path.setAttribute('stroke-linecap', 'round');
    } else if (type === 'secondary') {
        path.setAttribute('stroke', '#2196F3');
        path.setAttribute('stroke-linecap', 'round');
        path.setAttribute('stroke-dasharray', '5,3'); // 虚线效果
    } else {
        path.setAttribute('stroke', '#666666');
    }

    // 设置低于z-index，确保在节点下方
    path.style.zIndex = '-1';

    // 添加动画效果
    path.style.strokeDasharray = distance;
    path.style.strokeDashoffset = distance;
    path.style.animation = 'drawPath 1s ease-out forwards';
    path.style.animationDelay = type === 'secondary' ? '0.6s' : '0.3s';

    return path;
}

// 创建直线连接（备用）
function createConnection(x1, y1, x2, y2) {
    const connection = document.createElement('div');
    connection.className = 'mindmap-connection';

    const length = Math.sqrt((x2 - x1) ** 2 + (y2 - y1) ** 2);
    const angle = Math.atan2(y2 - y1, x2 - x1) * 180 / Math.PI;

    connection.style.width = `${length}px`;
    connection.style.left = `${x1}px`;
    connection.style.top = `${y1}px`;
    connection.style.transform = `rotate(${angle}deg)`;
    connection.style.transformOrigin = '0 50%';

    return connection;
}

// 获取学习资源
async function getResources(phaseIndex = null) {
    const thinkingId = addThinkingMessage();

    try {
        const response = await fetch(`${API_BASE_URL}/api/get-resources`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                domain: userProfile.domain,
                phase: phaseIndex !== null ? phaseIndex : userProfile.currentPhase,
                model: currentModel
            })
        });

        if (response.ok) {
            const data = await response.json();
            hideThinkingMessage(thinkingId);

            // 记录学习活动
            recordLearningActivity('resources');

            // 创建带有可点击URL的资源列表
            const resourcesWithUrls = data.resources.map(r => {
                // 为每个资源生成模拟URL（实际应用中应该是真实URL）
                const url = generateResourceUrl(r.title, r.type);
                return {
                    ...r,
                    url: url
                };
            });

            // 显示资源列表
            displayResourcesWithUrls(resourcesWithUrls);
        } else {
            hideThinkingMessage(thinkingId);
            addMessage('ai', '获取资源失败，请稍后重试。');
        }
    } catch (error) {
        console.error('Error:', error);
        hideThinkingMessage(thinkingId);
        addMessage('ai', '网络错误，请检查网络连接。');
    }
}

// 获取实践项目
async function getPracticeProjects() {
    const thinkingId = addThinkingMessage();

    try {
        const response = await fetch(`${API_BASE_URL}/api/get-projects`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                domain: userProfile.domain,
                phase: userProfile.currentPhase,
                background: userProfile.background,
                model: currentModel
            })
        });

        if (response.ok) {
            const data = await response.json();
            hideThinkingMessage(thinkingId);

            // 记录学习活动
            recordLearningActivity('projects');

            await streamMessage(`
**推荐的实践项目：**

${data.projects.map((p, index) => `${index + 1}. **${p.name}** (难度: ${p.difficulty})\n   ${p.description}`).join('\n\n')}

选择一个开始吧！我会指导您完成整个过程。
            `);
        } else {
            hideThinkingMessage(thinkingId);
            addMessage('ai', '获取项目推荐失败，请稍后重试。');
        }
    } catch (error) {
        console.error('Error:', error);
        hideThinkingMessage(thinkingId);
        addMessage('ai', '网络错误，请检查网络连接。');
    }
}

// 周度复盘
async function weeklyReview() {
    const weekNumber = Math.floor((new Date() - userProfile.startDate) / (7 * 24 * 60 * 60 * 1000)) + 1;
    const thinkingId = addThinkingMessage();

    try {
        const response = await fetch(`${API_BASE_URL}/api/weekly-review`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                domain: userProfile.domain,
                week: weekNumber,
                progress: userProfile.progress,
                phase: userProfile.currentPhase,
                model: currentModel
            })
        });

        if (response.ok) {
            const data = await response.json();
            hideThinkingMessage(thinkingId);

            // 记录学习活动
            recordLearningActivity('review');

            await streamMessage(data.review);
        } else {
            hideThinkingMessage(thinkingId);
            addMessage('ai', '生成复盘失败，请稍后重试。');
        }
    } catch (error) {
        console.error('Error:', error);
        hideThinkingMessage(thinkingId);
        addMessage('ai', '网络错误，请检查网络连接。');
    }
}

// 添加拖拽功能
function addDragFunctionality(svg, connectionsGroup, nodesGroup) {
    let isDragging = false;
    let dragStartX = 0;
    let dragStartY = 0;
    let currentTransform = { x: 0, y: 0 };

    // 检查是否已经有main-group，如果没有则创建
    let mainGroup = svg.querySelector('#main-group');
    if (!mainGroup) {
        mainGroup = document.createElementNS('http://www.w3.org/2000/svg', 'g');
        mainGroup.id = 'main-group';

        // 将现有的connections和nodes组移动到mainGroup中
        if (connectionsGroup.parentNode === svg) {
            svg.removeChild(connectionsGroup);
        }
        if (nodesGroup.parentNode === svg) {
            svg.removeChild(nodesGroup);
        }

        mainGroup.appendChild(connectionsGroup);
        mainGroup.appendChild(nodesGroup);
        svg.appendChild(mainGroup);
    }

    // 鼠标按下事件
    svg.addEventListener('mousedown', (e) => {
        // 只有点击空白区域才开始拖拽
        if (e.target === svg || e.target === mainGroup) {
            isDragging = true;
            dragStartX = e.clientX;
            dragStartY = e.clientY;
            svg.style.cursor = 'grabbing';
            e.preventDefault();
        }
    });

    // 鼠标移动事件
    svg.addEventListener('mousemove', (e) => {
        if (isDragging) {
            const deltaX = e.clientX - dragStartX;
            const deltaY = e.clientY - dragStartY;

            currentTransform.x += deltaX;
            currentTransform.y += deltaY;

            mainGroup.setAttribute('transform', `translate(${currentTransform.x}, ${currentTransform.y})`);

            dragStartX = e.clientX;
            dragStartY = e.clientY;
        }
    });

    // 鼠标释放事件
    svg.addEventListener('mouseup', () => {
        isDragging = false;
        svg.style.cursor = 'grab';
    });

    // 鼠标离开事件
    svg.addEventListener('mouseleave', () => {
        isDragging = false;
        svg.style.cursor = 'default';
    });

    // 设置初始样式
    svg.style.cursor = 'grab';

    // 双击重置位置
    svg.addEventListener('dblclick', () => {
        currentTransform = { x: 0, y: 0 };
        mainGroup.setAttribute('transform', 'translate(0, 0)');
        showNotification('已重置图谱位置', 'info');
    });
}

// 生成资源URL
function generateResourceUrl(title, type) {
    // 根据资源类型生成不同的URL
    const baseUrls = {
        '书籍': 'https://book.douban.com/subject/',
        '课程': 'https://www.coursera.org/learn/',
        '文章': 'https://medium.com/@learning/',
        '视频': 'https://www.youtube.com/watch?v=',
        '文档': 'https://docs.google.com/document/d/',
        '教程': 'https://www.udemy.com/course/',
        '博客': 'https://blog.example.com/',
        '论文': 'https://arxiv.org/abs/',
        '工具': 'https://github.com/',
        '网站': 'https://www.'
    };

    // 生成随机标识符
    const randomId = Math.random().toString(36).substr(2, 8);
    const baseUrl = baseUrls[type] || 'https://example.com/';

    // 将标题转换为适合URL的格式
    const urlTitle = title.toLowerCase()
        .replace(/[\u4e00-\u9fa5]/g, '') // 移除中文字符
        .replace(/[^a-z0-9]/g, '-')
        .replace(/-+/g, '-')
        .replace(/^-|-$/g, '');

    return `${baseUrl}${urlTitle}-${randomId}`;
}

// 显示带URL的资源列表
function displayResourcesWithUrls(resources) {
    // 直接创建HTML内容而不是Markdown
    let resourcesHtml = '<div class="resources-container">';
    resourcesHtml += '<h3>📚 为您推荐的学习资源：</h3>';

    resources.forEach((resource, index) => {
        resourcesHtml += `
            <div class="resource-item" data-index="${index}">
                <div class="resource-header">
                    <span class="resource-number">${index + 1}.</span>
                    <strong class="resource-title">${resource.title}</strong>
                    <span class="resource-type">(${resource.type})</span>
                </div>
                <div class="resource-description">${resource.description}</div>
                <div class="resource-link">
                    <a href="${resource.url}" class="clickable-resource-link" data-resource-index="${index}" data-resource-title="${resource.title}">
                        🔗 点击查看
                    </a>
                </div>
            </div>
        `;
    });

    resourcesHtml += '</div>';

    // 直接添加HTML内容到消息
    addHtmlMessage('ai', resourcesHtml);

    // 立即添加点击事件监听
    setTimeout(() => {
        addUrlClickListeners(resources);
    }, 100);
}

// 添加URL点击事件监听
function addUrlClickListeners(resources) {
    // 查找所有具有特定类名的链接
    const resourceLinks = document.querySelectorAll('.clickable-resource-link');

    resourceLinks.forEach((link) => {
        // 移除之前的事件监听器（避免重复绑定）
        link.replaceWith(link.cloneNode(true));
    });

    // 重新获取链接并添加事件监听
    const newResourceLinks = document.querySelectorAll('.clickable-resource-link');

    newResourceLinks.forEach((link) => {
        const resourceIndex = parseInt(link.getAttribute('data-resource-index'));
        const resourceTitle = link.getAttribute('data-resource-title');

        if (resourceIndex < resources.length) {
            link.addEventListener('click', (e) => {
                e.preventDefault();

                // 记录URL点击活动
                recordLearningActivity('url-click');

                // 显示点击反馈
                showUrlClickFeedback(resourceTitle);

                // 模拟打开链接的效果
                console.log(`模拟打开链接: ${link.href}`);
                console.log(`资源标题: ${resourceTitle}`);

                // 可选：在新窗口打开链接
                // window.open(link.href, '_blank');
            });

            // 添加视觉样式
            link.style.color = '#2196F3';
            link.style.textDecoration = 'underline';
            link.style.cursor = 'pointer';
            link.style.fontWeight = '500';
        }
    });
}

// 显示URL点击反馈
function showUrlClickFeedback(resourceTitle) {
    const message = `🔗 已点击资源：${resourceTitle}`;

    // 创建特殊的点击反馈Toast
    const toast = document.createElement('div');
    toast.className = 'url-click-toast';
    toast.textContent = message;

    toast.style.cssText = `
        position: fixed;
        bottom: 20px;
        right: 20px;
        background: #2196F3;
        color: white;
        padding: 10px 16px;
        border-radius: 6px;
        box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        z-index: 1000;
        font-size: 13px;
        font-weight: 500;
        animation: slideInUp 0.3s ease-out;
    `;

    document.body.appendChild(toast);

    // 2秒后移除
    setTimeout(() => {
        toast.style.animation = 'slideOutDown 0.3s ease-out';
        setTimeout(() => toast.remove(), 300);
    }, 2000);
}

// 显示通知
function showNotification(message, type = 'info') {
    // 简单的通知实现
    alert(message);
}

// 初始化
document.addEventListener('DOMContentLoaded', () => {
    // 加载学习活动数据
    loadLearningActivities();
    loadCurrentPhaseActivities();

    // 检查是否有保存的进度
    const savedProfile = localStorage.getItem('userProfile');
    if (savedProfile) {
        userProfile = JSON.parse(savedProfile);
        userProfile.startDate = new Date(userProfile.startDate);

        if (userProfile.domain) {
            setupSection.classList.remove('active');
            dashboardSection.classList.add('active');
            initializeDashboard();
        }
    }
});

// 保存进度
window.addEventListener('beforeunload', () => {
    localStorage.setItem('userProfile', JSON.stringify(userProfile));
});