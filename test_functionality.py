#!/usr/bin/env python3
"""
测试脚本：验证半年专家系统的功能完整性
"""

import requests
import json
import time
import sys

# 配置
BASE_URL = 'http://localhost:8000'

def test_health_check():
    """测试健康检查端点"""
    print("🔍 测试健康检查端点...")
    try:
        response = requests.get(f'{BASE_URL}/health')
        if response.status_code == 200:
            data = response.json()
            print(f"✅ 健康检查通过: {data['status']}")
            return True
        else:
            print(f"❌ 健康检查失败: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 健康检查异常: {e}")
        return False

def test_generate_plan():
    """测试学习计划生成"""
    print("\n🔍 测试学习计划生成...")
    try:
        payload = {
            "domain": "Python编程",
            "background": "计算机科学背景，有基础编程经验"
        }
        response = requests.post(f'{BASE_URL}/api/generate-plan', json=payload)
        if response.status_code == 200:
            data = response.json()
            print(f"✅ 学习计划生成成功")
            print(f"   领域: {data.get('domain')}")
            print(f"   欢迎消息: {data.get('welcome_message')[:100]}...")
            return True
        else:
            print(f"❌ 学习计划生成失败: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 学习计划生成异常: {e}")
        return False

def test_chat():
    """测试聊天功能"""
    print("\n🔍 测试聊天功能...")
    try:
        payload = {
            "message": "我应该如何开始学习Python？",
            "domain": "Python编程",
            "phase": "全局认知阶段",
            "context": {
                "currentPhase": 0,
                "progress": 5.0
            }
        }
        response = requests.post(f'{BASE_URL}/api/chat', json=payload)
        if response.status_code == 200:
            data = response.json()
            print(f"✅ 聊天功能正常")
            print(f"   AI回复: {data.get('response')[:100]}...")
            return True
        else:
            print(f"❌ 聊天功能失败: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 聊天功能异常: {e}")
        return False

def test_generate_mindmap():
    """测试知识图谱生成"""
    print("\n🔍 测试知识图谱生成...")
    try:
        payload = {
            "domain": "Python编程",
            "phase": 0
        }
        response = requests.post(f'{BASE_URL}/api/generate-mindmap', json=payload)
        if response.status_code == 200:
            data = response.json()
            print(f"✅ 知识图谱生成成功")
            mindmap = data.get('mindmap', {})
            if 'raw_text' in mindmap:
                print(f"   原始文本: {mindmap['raw_text'][:100]}...")
            else:
                print(f"   结构化数据: {list(mindmap.keys())}")
            return True
        else:
            print(f"❌ 知识图谱生成失败: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 知识图谱生成异常: {e}")
        return False

def test_get_resources():
    """测试学习资源获取"""
    print("\n🔍 测试学习资源获取...")
    try:
        payload = {
            "domain": "Python编程",
            "phase": 0
        }
        response = requests.post(f'{BASE_URL}/api/get-resources', json=payload)
        if response.status_code == 200:
            data = response.json()
            print(f"✅ 学习资源获取成功")
            resources = data.get('resources', [])
            print(f"   资源数量: {len(resources)}")
            if resources:
                print(f"   第一个资源: {resources[0].get('title', 'N/A')}")
            return True
        else:
            print(f"❌ 学习资源获取失败: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 学习资源获取异常: {e}")
        return False

def test_get_projects():
    """测试实践项目推荐"""
    print("\n🔍 测试实践项目推荐...")
    try:
        payload = {
            "domain": "Python编程",
            "phase": 0,
            "background": "有基础编程经验"
        }
        response = requests.post(f'{BASE_URL}/api/get-projects', json=payload)
        if response.status_code == 200:
            data = response.json()
            print(f"✅ 实践项目推荐成功")
            projects = data.get('projects', [])
            print(f"   项目数量: {len(projects)}")
            if projects:
                print(f"   第一个项目: {projects[0].get('name', 'N/A')}")
            return True
        else:
            print(f"❌ 实践项目推荐失败: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 实践项目推荐异常: {e}")
        return False

def test_weekly_review():
    """测试周度复盘"""
    print("\n🔍 测试周度复盘...")
    try:
        payload = {
            "domain": "Python编程",
            "week": 1,
            "progress": 10.0,
            "phase": 0
        }
        response = requests.post(f'{BASE_URL}/api/weekly-review', json=payload)
        if response.status_code == 200:
            data = response.json()
            print(f"✅ 周度复盘生成成功")
            print(f"   复盘内容: {data.get('review')[:100]}...")
            return True
        else:
            print(f"❌ 周度复盘生成失败: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 周度复盘生成异常: {e}")
        return False

def test_static_files():
    """测试静态文件服务"""
    print("\n🔍 测试静态文件服务...")
    try:
        # 测试主页
        response = requests.get(f'{BASE_URL}/')
        if response.status_code == 200 and 'html' in response.headers.get('content-type', ''):
            print("✅ 主页加载成功")
            
            # 测试CSS文件
            css_response = requests.get(f'{BASE_URL}/styles.css')
            if css_response.status_code == 200:
                print("✅ CSS文件加载成功")
                
                # 测试JS文件
                js_response = requests.get(f'{BASE_URL}/script.js')
                if js_response.status_code == 200:
                    print("✅ JavaScript文件加载成功")
                    return True
                else:
                    print("❌ JavaScript文件加载失败")
                    return False
            else:
                print("❌ CSS文件加载失败")
                return False
        else:
            print(f"❌ 主页加载失败: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 静态文件测试异常: {e}")
        return False

def main():
    """主测试函数"""
    print("🚀 开始测试半年专家系统功能...")
    print("=" * 50)
    
    # 等待服务器启动
    print("⏳ 等待服务器启动...")
    time.sleep(2)
    
    tests = [
        test_health_check,
        test_static_files,
        test_generate_plan,
        test_chat,
        test_generate_mindmap,
        test_get_resources,
        test_get_projects,
        test_weekly_review
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
        time.sleep(1)  # 避免请求过快
    
    print("\n" + "=" * 50)
    print(f"📊 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！系统功能完整。")
        return 0
    else:
        print("⚠️  部分测试失败，请检查相关功能。")
        return 1

if __name__ == "__main__":
    sys.exit(main())
